'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Filter, Grid, List, ChevronDown } from 'lucide-react';
import { Tool } from '@/lib/api';

interface CategoryFiltersClientProps {
  tools: Tool[];
  onFilteredToolsChange: (filteredTools: Tool[], viewMode: 'grid' | 'list', searchTerm: string) => void;
}

export default function CategoryFiltersClient({ tools, onFilteredToolsChange }: CategoryFiltersClientProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPricing, setSelectedPricing] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  const t = useTranslations('category_page');

  // Define pricing options with translations
  const pricingOptions = [
    { value: '', label: t('pricing_all') },
    { value: 'free', label: t('pricing_free') },
    { value: 'freemium', label: t('pricing_freemium') },
    { value: 'paid', label: t('pricing_paid') }
  ];

  // Define sort options with translations
  const sortOptions = [
    { value: 'popular', label: t('sort_popular') },
    { value: 'newest', label: t('sort_newest') },
    { value: 'name', label: t('sort_name') },
    { value: 'views', label: t('sort_views') }
  ];

  // Filter and sort tools
  React.useEffect(() => {
    const filteredTools = tools.filter(tool => {
      const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tool.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;
      
      return matchesSearch && matchesPricing;
    });

    const sortedTools = [...filteredTools].sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return (b.likes || 0) - (a.likes || 0);
        case 'views':
          return (b.views || 0) - (a.views || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'newest':
          if (a.createdAt && b.createdAt) {
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          }
          return 0;
        default:
          return 0;
      }
    });

    onFilteredToolsChange(sortedTools, viewMode, searchTerm);
  }, [tools, searchTerm, selectedPricing, sortBy, viewMode, onFilteredToolsChange]);

  return (
    <>
      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        {/* Search Bar */}
        <div className="relative mb-4">
          <input
            type="text"
            placeholder={t('search_placeholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <Filter className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
        </div>

        {/* Filter Toggle Button (Mobile) */}
        <div className="md:hidden mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          >
            <Filter className="mr-2 h-4 w-4" />
            {t('filter_options')}
            <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters */}
        <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('pricing')}</label>
            <select
              value={selectedPricing}
              onChange={(e) => setSelectedPricing(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {pricingOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('sort')}</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('view')}</label>
            <div className="flex rounded-lg border border-gray-300">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Grid className="h-4 w-4 mx-auto" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <List className="h-4 w-4 mx-auto" />
              </button>
            </div>
          </div>
        </div>
      </div>


    </>
  );
}
