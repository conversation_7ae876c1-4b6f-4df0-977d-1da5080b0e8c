import React from 'react';
import { Link } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';
import ErrorMessage from '@/components/ErrorMessage';
import { Tool } from '@/lib/api';
import { ArrowLeft } from 'lucide-react';
import { Locale } from '@/i18n/config';
import CategoryToolsDisplayClient from './CategoryToolsDisplayClient';

// 分类信息接口
interface CategoryInfo {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

interface CategoryPageServerProps {
  categoryInfo: CategoryInfo | null;
  tools: Tool[];
  error: string | null;
  locale: Locale;
}

export default async function CategoryPageServer({ categoryInfo, tools, error, locale }: CategoryPageServerProps) {
  const t = await getTranslations({ locale, namespace: 'category_page' });

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ErrorMessage message={error} />
      </div>
    );
  }

  if (!categoryInfo) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('not_found_title')}</h3>
          <p className="text-gray-600">{t('not_found_desc')}</p>
          <Link href="/categories" className="text-blue-600 hover:text-blue-700 mt-4 inline-block">
            {t('back_to_categories')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
        <Link href="/" className="hover:text-blue-600">{t('breadcrumb_home')}</Link>
        <span>/</span>
        <Link href="/categories" className="hover:text-blue-600">{t('breadcrumb_categories')}</Link>
        <span>/</span>
        <span className="text-gray-900">{categoryInfo.name}</span>
      </div>

      {/* Back Button */}
      <div className="mb-6">
        <Link
          href="/categories"
          className="inline-flex items-center text-blue-600 hover:text-blue-700"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back_to_categories')}
        </Link>
      </div>

      {/* Category Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <div
            className="w-16 h-16 rounded-lg flex items-center justify-center text-3xl"
            style={{ backgroundColor: categoryInfo.color }}
          >
            <span className="text-white">
              {categoryInfo.icon}
            </span>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {categoryInfo.name}
            </h1>
            <p className="text-lg text-gray-600">
              {t('tools_count', { count: categoryInfo.toolCount })}
            </p>
          </div>
        </div>
        <p className="text-gray-600 leading-relaxed">
          {categoryInfo.description}
        </p>
      </div>

      {/* Tools Display with Filters (Client Component) */}
      <CategoryToolsDisplayClient tools={tools} />

      {/* Related Categories */}
      <section className="mt-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('related_categories')}</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link
            href="/categories/text-generation"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">📝</span>
            <span className="text-sm font-medium text-gray-900">{t('text_generation')}</span>
          </Link>
          <Link
            href="/categories/image-generation"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">🎨</span>
            <span className="text-sm font-medium text-gray-900">{t('image_generation')}</span>
          </Link>
          <Link
            href="/categories/code-generation"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">💻</span>
            <span className="text-sm font-medium text-gray-900">{t('code_generation')}</span>
          </Link>
          <Link
            href="/categories/audio-processing"
            className="flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow"
          >
            <span className="text-2xl">🎵</span>
            <span className="text-sm font-medium text-gray-900">{t('audio_processing')}</span>
          </Link>
        </div>
      </section>
    </div>
  );
}
