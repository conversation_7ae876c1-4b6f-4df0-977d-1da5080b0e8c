'use client';

import React, { useState, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import ToolCard from '@/components/ToolCard';
import { Tool } from '@/lib/api';
import { Filter } from 'lucide-react';
import CategoryFiltersClient from './CategoryFiltersClient';

interface CategoryToolsDisplayClientProps {
  tools: Tool[];
}

export default function CategoryToolsDisplayClient({ tools }: CategoryToolsDisplayClientProps) {
  const [filteredTools, setFilteredTools] = useState<Tool[]>(tools);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');

  const t = useTranslations('category_page');

  const handleFilteredToolsChange = useCallback((newFilteredTools: Tool[], newViewMode: 'grid' | 'list', newSearchTerm: string) => {
    setFilteredTools(newFilteredTools);
    setViewMode(newViewMode);
    setSearchTerm(newSearchTerm);
  }, []);

  return (
    <>
      <CategoryFiltersClient 
        tools={tools} 
        onFilteredToolsChange={handleFilteredToolsChange}
      />

      {/* Results */}
      <div className="mb-6">
        <p className="text-gray-600">
          {t('results_count', { count: filteredTools.length })}
          {searchTerm && ` ${t('search_for', { term: searchTerm })}`}
        </p>
      </div>

      {/* Tools Grid/List */}
      {filteredTools.length > 0 ? (
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {filteredTools.map((tool) => (
            <ToolCard key={tool._id} tool={tool} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Filter className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('no_results_title')}</h3>
          <p className="text-gray-600">
            {t('no_results_desc')}
          </p>
        </div>
      )}
    </>
  );
}
