'use client';

import React from 'react';
import { Link } from '@/i18n/routing';
import { useTranslations } from 'next-intl';

interface CategoryCardProps {
  category: {
    _id: string;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    color?: string;
    toolCount: number;
  };
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {
  const t = useTranslations('common');
  return (
    <Link href={`/categories/${category.slug}`}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer" style={{ height: '100%' }}>
        <div className="p-6">
          <div className="flex items-center space-x-4 mb-4">
            <div 
              className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl"
              style={{ backgroundColor: category.color || '#3B82F6' }}
            >
              <span className="text-white">
                {category.icon || '🔧'}
              </span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {category.name}
              </h3>
              <p className="text-sm text-gray-500">
                {t('tools_count', { count: category.toolCount })}
              </p>
            </div>
          </div>
          
          <p className="text-gray-600 text-sm">
            {category.description}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;
