import { redirect } from '@/i18n/routing';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import CheckoutClient from '@/app/[locale]/payment/checkout/CheckoutClient';
import { AlertCircle } from 'lucide-react';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import User from '@/models/User';
import mongoose from 'mongoose';
import { getTranslations } from 'next-intl/server';

interface PageProps {
  searchParams: Promise<{ orderId?: string }>;
  params: Promise<{ locale: string }>;
}

async function getOrderData(orderId: string, userEmail: string) {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      return null;
    }

    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return null;
    }

    const order = await Order.findById(orderId).populate('toolId', 'name description');
    if (!order) {
      return null;
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return null;
    }

    // 转换为普通对象以避免循环引用和序列化问题
    const orderData = {
      _id: order._id.toString(),
      type: order.type,
      amount: order.amount,
      currency: order.currency,
      status: order.status,
      description: order.description,
      selectedLaunchDate: order.selectedLaunchDate ? order.selectedLaunchDate.toISOString() : null,
      createdAt: order.createdAt ? order.createdAt.toISOString() : null,
      paidAt: order.paidAt ? order.paidAt.toISOString() : null,
      tool: order.toolId ? {
        _id: order.toolId._id.toString(),
        name: order.toolId.name,
        description: order.toolId.description
      } : null,
      toolId: order.toolId ? order.toolId._id.toString() : null
    };

    return orderData;
  } catch (error) {
    console.error('Failed to fetch order:', error);
    return null;
  }
}

export default async function CheckoutPage({ searchParams, params }: PageProps) {
  const session = await getServerSession(authOptions);
  const { orderId } = await searchParams;
  const { locale } = await params;

  // 获取翻译
  const t = await getTranslations('checkout');

  // 检查用户是否已登录
  if (!session?.user?.email) {
    redirect({ href: '/', locale });
  }

  // 检查是否有订单ID
  if (!orderId) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('order_not_found')}</h1>
          <p className="text-gray-600 mb-4">{t('order_not_found_desc')}</p>
        </div>
      </div>
    );
  }

  // 获取订单数据
  const order = await getOrderData(orderId, session.user.email!);

  if (!order) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('order_not_found')}</h1>
          <p className="text-gray-600 mb-4">{t('order_deleted_desc')}</p>
        </div>
      </div>
    );
  }

  // 检查订单状态
  if (order.status === 'completed') {
    redirect({ href: `/submit/success?toolId=${order.toolId}`, locale });
  }

  if (order.status !== 'pending') {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('order_status_error')}</h1>
          <p className="text-gray-600 mb-4">{t('order_status_error_desc')}</p>
        </div>
      </div>
    );
  }

  return <CheckoutClient order={order} orderId={orderId} />;
}

