{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport {\n  useStripe,\n  useElements,\n  PaymentElement,\n  AddressElement\n} from '@stripe/react-stripe-js';\nimport { CreditCard, Loader2 } from 'lucide-react';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { Locale } from '@/i18n/config';\n\ninterface StripeCheckoutFormProps {\n  onSuccess: () => void;\n  amount: number;\n}\n\nexport default function StripeCheckoutForm({ onSuccess, amount }: StripeCheckoutFormProps) {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  const pathname = usePathname();\n  const t = useTranslations('payment');\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    setIsProcessing(true);\n    setErrorMessage('');\n\n    try {\n      // 确认支付\n      const { error } = await stripe.confirmPayment({\n        elements,\n        confirmParams: {\n          return_url: `${window.location.origin}/${currentLocale}/submit/success`,\n        },\n        redirect: 'if_required'\n      });\n\n      if (error) {\n        // 支付失败\n        if (error.type === 'card_error' || error.type === 'validation_error') {\n          setErrorMessage(error.message || t('payment_failed'));\n        } else {\n          setErrorMessage(t('payment_error'));\n        }\n      } else {\n        // 支付成功\n        onSuccess();\n      }\n    } catch (err) {\n      setErrorMessage(t('payment_processing_failed'));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 支付方式选择 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('payment_method')}\n        </label>\n        <PaymentElement \n          options={{\n            layout: 'tabs',\n            defaultValues: {\n              billingDetails: {\n                address: {\n                  country: 'CN'\n                }\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 账单地址 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('billing_address')}\n        </label>\n        <AddressElement \n          options={{\n            mode: 'billing',\n            defaultValues: {\n              address: {\n                country: 'CN'\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 错误信息 */}\n      {errorMessage && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-600 text-sm\">{errorMessage}</p>\n        </div>\n      )}\n\n      {/* 支付按钮 */}\n      <button\n        type=\"submit\"\n        disabled={!stripe || !elements || isProcessing}\n        className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors\"\n      >\n        {isProcessing ? (\n          <>\n            <Loader2 className=\"h-5 w-5 mr-2 animate-spin\" />\n            {t('processing_payment')}\n          </>\n        ) : (\n          <>\n            <CreditCard className=\"h-5 w-5 mr-2\" />\n            {t('pay_now', { amount: formatStripeAmount(amount) })}\n          </>\n        )}\n      </button>\n\n      {/* 安全提示 */}\n      <div className=\"text-center\">\n        <p className=\"text-gray-500 text-xs\">\n          {t('security_notice')}\n        </p>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AACA;AAZA;;;;;;;;AAoBe,SAAS,mBAAmB,EAAE,SAAS,EAAE,MAAM,EAA2B;IACvF,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,OAAO;YACP,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,cAAc,CAAC;gBAC5C;gBACA,eAAe;oBACb,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,eAAe,CAAC;gBACzE;gBACA,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,OAAO;gBACP,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,oBAAoB;oBACpE,gBAAgB,MAAM,OAAO,IAAI,EAAE;gBACrC,OAAO;oBACL,gBAAgB,EAAE;gBACpB;YACF,OAAO;gBACL,OAAO;gBACP;YACF;QACF,EAAE,OAAO,KAAK;YACZ,gBAAgB,EAAE;QACpB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,8OAAC,oLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,QAAQ;4BACR,eAAe;gCACb,gBAAgB;oCACd,SAAS;wCACP,SAAS;oCACX;gCACF;4BACF;wBACF;;;;;;;;;;;;0BAKJ,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,8OAAC,oLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,MAAM;4BACN,eAAe;gCACb,SAAS;oCACP,SAAS;gCACX;4BACF;wBACF;;;;;;;;;;;;YAKH,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAKzC,8OAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU,CAAC,YAAY;gBAClC,WAAU;0BAET,6BACC;;sCACE,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;iDAGL;;sCACE,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBACrB,EAAE,WAAW;4BAAE,QAAQ,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE;wBAAQ;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;AACxC", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n\n  // 订单API\n  async getOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}`);\n  }\n\n  async processOrderPayment(id: string, data: {\n    paymentMethod?: string;\n  }): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}/pay`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;IAEA,QAAQ;IACR,MAAM,SAAS,EAAU,EAA6B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,IAAI;IAC1C;IAEA,MAAM,oBAAoB,EAAU,EAAE,IAErC,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/payment/checkout/CheckoutClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from '@/i18n/routing';\nimport { loadStripe } from '@stripe/stripe-js';\nimport { Elements } from '@stripe/react-stripe-js';\nimport StripeCheckoutForm from '@/components/StripeCheckoutForm';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';\nimport { apiClient } from '@/lib/api';\nimport { useTranslations } from 'next-intl';\n\n// 初始化Stripe\nconst stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);\n\ninterface OrderData {\n  _id: string;\n  type: string;\n  amount: number;\n  currency: string;\n  status: string;\n  description: string;\n  selectedLaunchDate: string | null;\n  createdAt: string | null;\n  paidAt: string | null;\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n  } | null;\n  toolId: string | null;\n}\n\ninterface CheckoutClientProps {\n  order: OrderData;\n  orderId: string;\n}\n\nexport default function CheckoutClient({ order, orderId }: CheckoutClientProps) {\n  const router = useRouter();\n  const [clientSecret, setClientSecret] = useState<string>('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n\n  // 获取翻译\n  const t = useTranslations('checkout');\n\n  useEffect(() => {\n    createPaymentIntent();\n  }, [orderId]);\n\n  const createPaymentIntent = async () => {\n    try {\n      const response = await fetch('/api/stripe/create-payment-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ orderId }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setClientSecret(data.data.clientSecret);\n      } else {\n        setError(data.message || t('payment_error_title'));\n      }\n    } catch (error) {\n      console.error('创建支付意图失败:', error);\n      setError(t('payment_error_title'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePaymentSuccess = async () => {\n    try {\n      // 调用订单支付接口确保状态更新\n      const response = await apiClient.processOrderPayment(orderId, {\n        paymentMethod: 'stripe'\n      });\n\n      if (response.success) {\n        console.log('订单状态更新成功:', response.data);\n      } else {\n        console.warn('订单状态更新失败:', response.error);\n        // 即使状态更新失败，也继续跳转，因为Webhook会处理\n      }\n    } catch (error) {\n      console.error('调用订单支付接口失败:', error);\n      // 即使接口调用失败，也继续跳转，因为Webhook会处理\n    }\n\n    // 使用当前语言环境进行路由跳转\n    router.push(`/submit/success?toolId=${order.toolId}&paid=true`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">{t('creating_payment_session')}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{t('payment_error_title')}</h1>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button\n            onClick={() => router.push('/submit')}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            {t('back_to_submit')}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"text-center mb-8\">\n        <CreditCard className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          {t('page_title')}\n        </h1>\n        <p className=\"text-lg text-gray-600\">\n          {t('page_subtitle')}\n        </p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6 mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('order_details')}</h2>\n\n        <div className=\"space-y-3\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('service_type')}</span>\n            <span className=\"font-medium\">{t('tool_priority_launch')}</span>\n          </div>\n\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('tool_name')}</span>\n            <span className=\"font-medium\">{order.tool?.name || t('loading_placeholder')}</span>\n          </div>\n\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('launch_date')}</span>\n            <span className=\"font-medium\">\n              {order.selectedLaunchDate ? new Date(order.selectedLaunchDate).toLocaleDateString() : t('loading_placeholder')}\n            </span>\n          </div>\n\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('order_number')}</span>\n            <span className=\"font-medium text-sm\">{order._id}</span>\n          </div>\n\n          <hr className=\"my-4\" />\n\n          <div className=\"flex justify-between text-lg font-semibold\">\n            <span>{t('total')}</span>\n            <span className=\"text-blue-600\">{formatStripeAmount(order.amount)}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Service Features */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">\n          {t('priority_service_title')}\n        </h3>\n        <ul className=\"space-y-2\">\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_any_date')}\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_priority_review')}\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_homepage_featured')}\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_dedicated_support')}\n          </li>\n        </ul>\n      </div>\n\n      {/* Security Notice */}\n      <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center text-gray-700\">\n          <Shield className=\"h-5 w-5 mr-2 text-green-500\" />\n          <span className=\"text-sm\">\n            {t('security_notice')}\n          </span>\n        </div>\n      </div>\n\n      {/* Stripe Payment Form */}\n      {clientSecret && (\n        <Elements\n          stripe={stripePromise}\n          options={{\n            clientSecret,\n            appearance: {\n              theme: 'stripe',\n              variables: {\n                colorPrimary: '#2563eb',\n              }\n            }\n          }}\n        >\n          <StripeCheckoutForm\n            onSuccess={handlePaymentSuccess}\n            amount={order.amount}\n          />\n        </Elements>\n      )}\n\n      <p className=\"text-gray-500 text-sm mt-4 text-center\">\n        {t('terms_notice')}\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,YAAY;AACZ,MAAM,gBAAgB,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD;AAyBhB,SAAS,eAAe,EAAE,KAAK,EAAE,OAAO,EAAuB;IAC5E,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,OAAO;IACP,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI,CAAC,YAAY;YACxC,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,SAAS;gBAC5D,eAAe;YACjB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,QAAQ,GAAG,CAAC,aAAa,SAAS,IAAI;YACxC,OAAO;gBACL,QAAQ,IAAI,CAAC,aAAa,SAAS,KAAK;YACxC,8BAA8B;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,8BAA8B;QAChC;QAEA,iBAAiB;QACjB,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,MAAM,MAAM,CAAC,UAAU,CAAC;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAsB,EAAE;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAAyC,EAAE;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4C,EAAE;;;;;;kCAE5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDAAe,EAAE;;;;;;;;;;;;0CAGnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDAAe,MAAM,IAAI,EAAE,QAAQ,EAAE;;;;;;;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDACb,MAAM,kBAAkB,GAAG,IAAI,KAAK,MAAM,kBAAkB,EAAE,kBAAkB,KAAK,EAAE;;;;;;;;;;;;0CAI5F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDAAuB,MAAM,GAAG;;;;;;;;;;;;0CAGlD,8OAAC;gCAAG,WAAU;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,EAAE;;;;;;kDACT,8OAAC;wCAAK,WAAU;kDAAiB,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;0CAEL,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;0CAEL,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;0CAEL,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAK,WAAU;sCACb,EAAE;;;;;;;;;;;;;;;;;YAMR,8BACC,8OAAC,oLAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS;oBACP;oBACA,YAAY;wBACV,OAAO;wBACP,WAAW;4BACT,cAAc;wBAChB;oBACF;gBACF;0BAEA,cAAA,8OAAC,wIAAA,CAAA,UAAkB;oBACjB,WAAW;oBACX,QAAQ,MAAM,MAAM;;;;;;;;;;;0BAK1B,8OAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;;;;;;;AAIX", "debugId": null}}]}