{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["// This file is deprecated - Footer functionality moved to layout/Footer.tsx\n"], "names": [], "mappings": "AAAA,4EAA4E", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts"], "sourcesContent": ["import { Tool } from '@/lib/api';\n\nconst baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';\n\n// 网站基础结构化数据\nexport function getWebsiteStructuredData() {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    \"name\": \"AI工具导航\",\n    \"description\": \"发现最好的AI工具，提升您的工作效率和创造力\",\n    \"url\": baseUrl,\n    \"potentialAction\": {\n      \"@type\": \"SearchAction\",\n      \"target\": {\n        \"@type\": \"EntryPoint\",\n        \"urlTemplate\": `${baseUrl}/tools?search={search_term_string}`\n      },\n      \"query-input\": \"required name=search_term_string\"\n    },\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"url\": baseUrl\n    }\n  };\n}\n\n// 组织结构化数据\nexport function getOrganizationStructuredData() {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"AI工具导航\",\n    \"description\": \"专业的AI工具发现和推荐平台\",\n    \"url\": baseUrl,\n    \"logo\": `${baseUrl}/logo.png`,\n    \"sameAs\": [\n      // 可以添加社交媒体链接\n    ]\n  };\n}\n\n// 工具详情页结构化数据\nexport function getToolStructuredData(tool: Tool) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"SoftwareApplication\",\n    \"name\": tool.name,\n    \"description\": tool.description,\n    \"url\": tool.website,\n    \"applicationCategory\": \"AI工具\",\n    \"operatingSystem\": \"Web\",\n    \"offers\": {\n      \"@type\": \"Offer\",\n      \"price\": tool.pricing === 'free' ? \"0\" : undefined,\n      \"priceCurrency\": \"USD\",\n      \"availability\": \"https://schema.org/InStock\"\n    },\n    \"aggregateRating\": tool.likes ? {\n      \"@type\": \"AggregateRating\",\n      \"ratingValue\": Math.min(5, Math.max(1, (tool.likes / 10) + 3)), // 简单的评分算法\n      \"reviewCount\": tool.likes,\n      \"bestRating\": 5,\n      \"worstRating\": 1\n    } : undefined,\n    \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`,\n    \"datePublished\": tool.launchDate,\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"url\": baseUrl\n    }\n  };\n}\n\n// 面包屑导航结构化数据\nexport function getBreadcrumbStructuredData(items: Array<{name: string, url: string}>) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    \"itemListElement\": items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"name\": item.name,\n      \"item\": `${baseUrl}${item.url}`\n    }))\n  };\n}\n\n// 工具列表页结构化数据\nexport function getToolListStructuredData(tools: Tool[], category?: string) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ItemList\",\n    \"name\": category ? `${category} AI工具` : \"AI工具列表\",\n    \"description\": category ? `发现最好的${category} AI工具` : \"发现最好的AI工具\",\n    \"numberOfItems\": tools.length,\n    \"itemListElement\": tools.map((tool, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"item\": {\n        \"@type\": \"SoftwareApplication\",\n        \"name\": tool.name,\n        \"description\": tool.description,\n        \"url\": `${baseUrl}/tools/${tool._id}`,\n        \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`\n      }\n    }))\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AAG7C,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,mBAAmB;YACjB,SAAS;YACT,UAAU;gBACR,SAAS;gBACT,eAAe,GAAG,QAAQ,kCAAkC,CAAC;YAC/D;YACA,eAAe;QACjB;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAGO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,QAAQ,GAAG,QAAQ,SAAS,CAAC;QAC7B,UAAU,EAET;IACH;AACF;AAGO,SAAS,sBAAsB,IAAU;IAC9C,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ,KAAK,IAAI;QACjB,eAAe,KAAK,WAAW;QAC/B,OAAO,KAAK,OAAO;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,UAAU;YACR,SAAS;YACT,SAAS,KAAK,OAAO,KAAK,SAAS,MAAM;YACzC,iBAAiB;YACjB,gBAAgB;QAClB;QACA,mBAAmB,KAAK,KAAK,GAAG;YAC9B,SAAS;YACT,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,AAAC,KAAK,KAAK,GAAG,KAAM;YAC3D,eAAe,KAAK,KAAK;YACzB,cAAc;YACd,eAAe;QACjB,IAAI;QACJ,SAAS,KAAK,IAAI,IAAI,GAAG,QAAQ,uBAAuB,CAAC;QACzD,iBAAiB,KAAK,UAAU;QAChC,aAAa;YACX,SAAS;YACT,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAGO,SAAS,4BAA4B,KAAyC;IACnF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,GAAG,UAAU,KAAK,GAAG,EAAE;YACjC,CAAC;IACH;AACF;AAGO,SAAS,0BAA0B,KAAa,EAAE,QAAiB;IACxE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ,WAAW,GAAG,SAAS,KAAK,CAAC,GAAG;QACxC,eAAe,WAAW,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,GAAG;QACpD,iBAAiB,MAAM,MAAM;QAC7B,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ;oBACN,SAAS;oBACT,QAAQ,KAAK,IAAI;oBACjB,eAAe,KAAK,WAAW;oBAC/B,OAAO,GAAG,QAAQ,OAAO,EAAE,KAAK,GAAG,EAAE;oBACrC,SAAS,KAAK,IAAI,IAAI,GAAG,QAAQ,uBAAuB,CAAC;gBAC3D;YACF,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/contact/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { Metadata } from 'next';\nimport Layout from '@/components/Layout';\nimport { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';\nimport { Mail, MessageSquare, Clock, MapPin } from 'lucide-react';\n\n// 生成静态metadata\nexport const metadata: Metadata = {\n  title: '联系我们 - AI工具导航',\n  description: '联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议。我们期待与您的交流。',\n  keywords: '联系我们,技术支持,商务合作,反馈建议,AI工具导航',\n  authors: [{ name: 'AI工具导航团队' }],\n  robots: {\n    index: true,\n    follow: true,\n  },\n  openGraph: {\n    type: 'website',\n    locale: 'zh_CN',\n    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/contact`,\n    siteName: 'AI工具导航',\n    title: '联系我们 - AI工具导航',\n    description: '联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议。',\n    images: [\n      {\n        url: '/og-contact.jpg',\n        width: 1200,\n        height: 630,\n        alt: '联系我们 - AI工具导航',\n      },\n    ],\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: '联系我们 - AI工具导航',\n    description: '联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议。',\n    images: ['/og-contact.jpg'],\n  },\n  alternates: {\n    canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/contact`,\n  },\n};\n\nexport default function ContactPage() {\n  // 生成结构化数据\n  const breadcrumbStructuredData = getBreadcrumbStructuredData([\n    { name: '首页', url: '/' },\n    { name: '联系我们', url: '/contact' }\n  ]);\n\n  // 生成联系页面结构化数据\n  const contactStructuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ContactPage\",\n    \"name\": \"联系我们 - AI工具导航\",\n    \"description\": \"联系AI工具导航团队，获取技术支持、商务合作或提供反馈建议\",\n    \"url\": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/contact`,\n    \"mainEntity\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"contactPoint\": [\n        {\n          \"@type\": \"ContactPoint\",\n          \"contactType\": \"customer service\",\n          \"email\": \"<EMAIL>\",\n          \"availableLanguage\": \"Chinese\"\n        },\n        {\n          \"@type\": \"ContactPoint\",\n          \"contactType\": \"business\",\n          \"email\": \"<EMAIL>\",\n          \"availableLanguage\": \"Chinese\"\n        }\n      ]\n    }\n  };\n\n  return (\n    <Layout>\n      {/* 结构化数据 */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(breadcrumbStructuredData)\n        }}\n      />\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(contactStructuredData)\n        }}\n      />\n\n      {/* 面包屑导航 */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n        <nav className=\"flex\" aria-label=\"面包屑导航\">\n          <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n            <li className=\"inline-flex items-center\">\n              <a\n                href=\"/\"\n                className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600\"\n              >\n                首页\n              </a>\n            </li>\n            <li>\n              <div className=\"flex items-center\">\n                <svg className=\"w-6 h-6 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                <span className=\"ml-1 text-sm font-medium text-gray-500 md:ml-2\">联系我们</span>\n              </div>\n            </li>\n          </ol>\n        </nav>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            联系我们\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            我们很乐意听到您的声音。无论是技术支持、商务合作还是反馈建议，请随时与我们联系。\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">联系方式</h2>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <Mail className=\"h-6 w-6 text-blue-600 mt-1\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">邮箱联系</h3>\n                  <p className=\"text-gray-600 mt-1\">\n                    一般咨询：<a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:text-blue-800\"><EMAIL></a>\n                  </p>\n                  <p className=\"text-gray-600\">\n                    商务合作：<a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:text-blue-800\"><EMAIL></a>\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <Clock className=\"h-6 w-6 text-blue-600 mt-1\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">响应时间</h3>\n                  <p className=\"text-gray-600 mt-1\">\n                    我们通常在24小时内回复您的邮件\n                  </p>\n                  <p className=\"text-gray-600\">\n                    工作时间：周一至周五 9:00-18:00 (北京时间)\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <MessageSquare className=\"h-6 w-6 text-blue-600 mt-1\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">常见问题</h3>\n                  <p className=\"text-gray-600 mt-1\">\n                    在联系我们之前，您可以查看我们的常见问题解答\n                  </p>\n                  <a href=\"/faq\" className=\"text-blue-600 hover:text-blue-800\">查看FAQ →</a>\n                </div>\n              </div>\n            </div>\n\n            {/* Contact Types */}\n            <div className=\"mt-12\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-6\">我们能为您提供什么帮助？</h3>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                <div className=\"bg-blue-50 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">技术支持</h4>\n                  <p className=\"text-sm text-gray-600\">网站使用问题、账户问题、功能咨询</p>\n                </div>\n                <div className=\"bg-green-50 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">商务合作</h4>\n                  <p className=\"text-sm text-gray-600\">工具推广、广告投放、战略合作</p>\n                </div>\n                <div className=\"bg-purple-50 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">内容建议</h4>\n                  <p className=\"text-sm text-gray-600\">工具推荐、分类建议、功能改进</p>\n                </div>\n                <div className=\"bg-orange-50 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">媒体合作</h4>\n                  <p className=\"text-sm text-gray-600\">采访邀请、新闻发布、行业报告</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div className=\"bg-gray-50 rounded-lg p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">发送消息</h2>\n            <form className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  姓名 *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"请输入您的姓名\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  邮箱 *\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"请输入您的邮箱地址\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  主题 *\n                </label>\n                <select\n                  id=\"subject\"\n                  name=\"subject\"\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">请选择咨询类型</option>\n                  <option value=\"technical\">技术支持</option>\n                  <option value=\"business\">商务合作</option>\n                  <option value=\"content\">内容建议</option>\n                  <option value=\"media\">媒体合作</option>\n                  <option value=\"other\">其他</option>\n                </select>\n              </div>\n\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  消息内容 *\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  rows={6}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"请详细描述您的问题或需求...\"\n                ></textarea>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium\"\n              >\n                发送消息\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAW;KAAE;IAC/B,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,8BAA8B,QAAQ,CAAC;QACnF,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAkB;IAC7B;IACA,YAAY;QACV,WAAW,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,8BAA8B,QAAQ,CAAC;IAC3F;AACF;AAEe,SAAS;IACtB,UAAU;IACV,MAAM,2BAA2B,CAAA,GAAA,mIAAA,CAAA,8BAA2B,AAAD,EAAE;QAC3D;YAAE,MAAM;YAAM,KAAK;QAAI;QACvB;YAAE,MAAM;YAAQ,KAAK;QAAW;KACjC;IAED,cAAc;IACd,MAAM,wBAAwB;QAC5B,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,8BAA8B,QAAQ,CAAC;QACrF,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,gBAAgB;gBACd;oBACE,SAAS;oBACT,eAAe;oBACf,SAAS;oBACT,qBAAqB;gBACvB;gBACA;oBACE,SAAS;oBACT,eAAe;oBACf,SAAS;oBACT,qBAAqB;gBACvB;aACD;QACH;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;;0BAEL,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAEF,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAO,cAAW;8BAC/B,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;0CAIH,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;sDAE3J,8OAAC;4CAAK,WAAU;sDAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,8OAAC;gEAAE,WAAU;;oEAAqB;kFAC3B,8OAAC;wEAAE,MAAK;wEAAqC,WAAU;kFAAoC;;;;;;;;;;;;0EAElG,8OAAC;gEAAE,WAAU;;oEAAgB;kFACtB,8OAAC;wEAAE,MAAK;wEAAsC,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;0DAKvG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAGlC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAMjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;kEAE3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAGlC,8OAAC;gEAAE,MAAK;gEAAO,WAAU;0EAAoC;;;;;;;;;;;;;;;;;;;;;;;;kDAMnE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAM;wDACN,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,EAAK;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "file": "message-square.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/message-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z', key: '1lielz' }],\n];\n\n/**\n * @component @name MessageSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquare = createLucideIcon('message-square', __iconNode);\n\nexport default MessageSquare;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChG;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,GAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BAC<PERSON>,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}