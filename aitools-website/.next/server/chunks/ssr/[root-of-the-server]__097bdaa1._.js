module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/i18n/routing.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Link": (()=>Link),
    "redirect": (()=>redirect),
    "routing": (()=>routing),
    "usePathname": (()=>usePathname),
    "useRouter": (()=>useRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/routing/defineRouting.js [app-ssr] (ecmascript) <export default as defineRouting>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2f$react$2d$client$2f$createNavigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__createNavigation$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js [app-ssr] (ecmascript) <export default as createNavigation>");
// 导出安全路由助手（推荐在客户端组件中使用）
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safe$2d$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/safe-routing.ts [app-ssr] (ecmascript)");
;
;
const routing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__["defineRouting"])({
    // 支持的语言列表
    locales: [
        'en',
        'zh'
    ],
    // 默认语言
    defaultLocale: 'en',
    // 语言前缀配置 - 始终显示语言前缀
    localePrefix: 'always',
    // 启用语言检测
    localeDetection: true,
    // 启用备用链接
    alternateLinks: true,
    // 语言 cookie 配置
    localeCookie: {
        name: 'NEXT_LOCALE',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 365 // 1 year
    }
});
const { Link, redirect, usePathname, useRouter } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2f$react$2d$client$2f$createNavigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__createNavigation$3e$__["createNavigation"])(routing);
;
}}),
"[project]/src/i18n/routing.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safe$2d$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/safe-routing.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/url-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// URL工具函数 - 处理国际化URL的规范化
// 支持的语言列表
__turbopack_context__.s({
    "addLocalePrefix": (()=>addLocalePrefix),
    "buildSafeUrl": (()=>buildSafeUrl),
    "defaultLocale": (()=>defaultLocale),
    "extractLocaleFromPath": (()=>extractLocaleFromPath),
    "hasValidLocalePrefix": (()=>hasValidLocalePrefix),
    "locales": (()=>locales),
    "normalizeUrlPath": (()=>normalizeUrlPath),
    "removeLocalePrefix": (()=>removeLocalePrefix),
    "validateAndFixUrl": (()=>validateAndFixUrl)
});
const locales = [
    'en',
    'zh'
];
const defaultLocale = 'en';
function normalizeUrlPath(path, currentLocale = defaultLocale) {
    // 移除开头和结尾的斜杠
    const cleanPath = path.replace(/^\/+|\/+$/g, '');
    // 如果路径为空，返回根路径
    if (!cleanPath) {
        return '/';
    }
    // 分割路径段
    const segments = cleanPath.split('/').filter(Boolean);
    if (segments.length === 0) {
        return '/';
    }
    const firstSegment = segments[0];
    // 检查第一个段是否是locale
    if (locales.includes(firstSegment)) {
        // 检查是否有重复的locale
        const secondSegment = segments[1];
        if (secondSegment && locales.includes(secondSegment)) {
            // 移除重复的locale，保留第二个
            return '/' + segments.slice(1).join('/');
        }
        // 如果已经有正确的locale前缀，直接返回
        return '/' + segments.join('/');
    }
    // 如果没有locale前缀，添加当前locale
    return `/${currentLocale}/${segments.join('/')}`;
}
function hasValidLocalePrefix(path) {
    const segments = path.replace(/^\/+/, '').split('/').filter(Boolean);
    return segments.length > 0 && locales.includes(segments[0]);
}
function extractLocaleFromPath(path) {
    const segments = path.replace(/^\/+/, '').split('/').filter(Boolean);
    const firstSegment = segments[0];
    if (firstSegment && locales.includes(firstSegment)) {
        return firstSegment;
    }
    return defaultLocale;
}
function removeLocalePrefix(path) {
    const segments = path.replace(/^\/+/, '').split('/').filter(Boolean);
    if (segments.length > 0 && locales.includes(segments[0])) {
        const remainingPath = segments.slice(1).join('/');
        return remainingPath ? `/${remainingPath}` : '/';
    }
    return path;
}
function addLocalePrefix(path, locale = defaultLocale) {
    const cleanPath = removeLocalePrefix(path);
    if (cleanPath === '/') {
        return `/${locale}`;
    }
    return `/${locale}${cleanPath}`;
}
function buildSafeUrl(path, locale = defaultLocale, params) {
    // 规范化路径
    const normalizedPath = normalizeUrlPath(path, locale);
    // 添加查询参数
    if (params && Object.keys(params).length > 0) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value])=>{
            searchParams.append(key, String(value));
        });
        return `${normalizedPath}?${searchParams.toString()}`;
    }
    return normalizedPath;
}
function validateAndFixUrl(url, currentLocale = defaultLocale) {
    try {
        const urlObj = new URL(url, 'http://localhost');
        const originalPath = urlObj.pathname;
        const normalizedPath = normalizeUrlPath(originalPath, currentLocale);
        const needsRedirect = originalPath !== normalizedPath;
        const fixedUrl = needsRedirect ? `${normalizedPath}${urlObj.search}${urlObj.hash}` : url;
        return {
            fixedUrl,
            needsRedirect
        };
    } catch  {
        // 如果URL解析失败，返回安全的默认值
        return {
            fixedUrl: `/${currentLocale}`,
            needsRedirect: true
        };
    }
}
;
}}),
"[project]/src/lib/safe-routing.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSafeHref": (()=>createSafeHref),
    "safeLocationHref": (()=>safeLocationHref),
    "useSafePathname": (()=>useSafePathname),
    "useSafeRouter": (()=>useSafeRouter),
    "useUrlValidation": (()=>useUrlValidation),
    "withUrlValidation": (()=>withUrlValidation)
});
// 安全路由助手 - 包装next-intl路由功能，提供额外的URL安全检查
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/url-utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function useSafeRouter() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useRouter"])();
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLocale"])();
    return {
        ...router,
        /**
     * 安全的push方法，自动处理locale前缀
     */ push: (href, options)=>{
            const normalizedHref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeUrlPath"])(href, locale);
            console.log(`🔄 Safe Router Push: ${href} -> ${normalizedHref}`);
            return router.push(normalizedHref, options);
        },
        /**
     * 安全的replace方法，自动处理locale前缀
     */ replace: (href, options)=>{
            const normalizedHref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeUrlPath"])(href, locale);
            console.log(`🔄 Safe Router Replace: ${href} -> ${normalizedHref}`);
            return router.replace(normalizedHref, options);
        },
        /**
     * 安全的prefetch方法，自动处理locale前缀
     */ prefetch: (href, options)=>{
            const normalizedHref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeUrlPath"])(href, locale);
            return router.prefetch(normalizedHref, options);
        },
        /**
     * 构建安全的URL
     */ buildUrl: (path, params)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildSafeUrl"])(path, locale, params);
        },
        /**
     * 验证当前URL并在需要时重定向
     */ validateCurrentUrl: ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return false;
        }
    };
}
function useSafePathname() {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["usePathname"])();
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLocale"])();
    return {
        /**
     * 获取当前路径名
     */ pathname,
        /**
     * 获取规范化的路径名
     */ normalizedPathname: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeUrlPath"])(pathname, locale),
        /**
     * 检查当前路径是否需要规范化
     */ needsNormalization: pathname !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeUrlPath"])(pathname, locale),
        /**
     * 获取不含locale前缀的路径
     */ pathWithoutLocale: pathname.replace(new RegExp(`^/${locale}`), '') || '/'
    };
}
function safeLocationHref(url, currentLocale) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
    const locale = undefined;
    const fixedUrl = undefined;
}
function createSafeHref(path, locale, params) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$url$2d$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildSafeUrl"])(path, locale, params);
}
function useUrlValidation() {
    const router = useSafeRouter();
    // 在客户端挂载时验证URL
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function withUrlValidation(Component) {
    return function UrlValidatedComponent(props) {
        useUrlValidation();
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Component, props);
    };
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__097bdaa1._.js.map