{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport default function middleware(request: NextRequest) {\n  console.log(`🔍 MIDDLEWARE CALLED: ${request.nextUrl.pathname}`);\n  \n  const pathname = request.nextUrl.pathname;\n  \n  // 测试重定向\n  if (pathname === '/submit') {\n    console.log(`🔄 Redirecting /submit to /en/submit`);\n    return NextResponse.redirect(new URL('/en/submit', request.url));\n  }\n  \n  if (pathname === '/en/zh/submit') {\n    console.log(`🔄 Redirecting /en/zh/submit to /en/submit`);\n    return NextResponse.redirect(new URL('/en/submit', request.url));\n  }\n  \n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEe,SAAS,WAAW,OAAoB;IACrD,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;IAE/D,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,QAAQ;IACR,IAAI,aAAa,WAAW;QAC1B,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC;QAClD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,IAAI,aAAa,iBAAiB;QAChC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;QACxD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}