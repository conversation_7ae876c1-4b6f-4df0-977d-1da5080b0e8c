{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;AACH,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,6HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,SAAS,6HAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\n\nexport const routing = defineRouting({\n  // 支持的语言列表\n  locales: ['en', 'zh'],\n\n  // 默认语言\n  defaultLocale: 'en',\n\n  // 语言前缀配置 - 始终显示语言前缀\n  localePrefix: 'always',\n\n  // 启用语言检测\n  localeDetection: true,\n\n  // 启用备用链接\n  alternateLinks: true,\n\n  // 语言 cookie 配置\n  localeCookie: {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 * 365 // 1 year\n  }\n});\n\n// 轻量级的包装器，围绕 Next.js 的导航 API\n// 它们将自动处理用户的语言环境\nexport const { Link, redirect, usePathname, useRouter } = createNavigation(routing);\n\n// 导出安全路由助手（推荐在客户端组件中使用）\nexport { useSafeRouter, useSafePathname, safeLocationHref, createSafeHref, useUrlValidation, withUrlValidation } from '@/lib/safe-routing';\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,UAAU;IACV,SAAS;QAAC;QAAM;KAAK;IAErB,OAAO;IACP,eAAe;IAEf,oBAAoB;IACpB,cAAc;IAEd,SAAS;IACT,iBAAiB;IAEjB,SAAS;IACT,gBAAgB;IAEhB,eAAe;IACf,cAAc;QACZ,MAAM;QACN,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK,IAAI,SAAS;IACtC;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,mBAAgB,AAAD,EAAE"}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport { NextRequest, NextResponse } from 'next/server';\nimport { routing } from './i18n/routing';\n\n// 支持的语言列表\nconst locales = ['en', 'zh'];\nconst defaultLocale = 'en';\n\n// 创建 next-intl 中间件\nconst i18nMiddleware = createMiddleware(routing);\n\n/**\n * 规范化URL路径，处理重复locale和缺失locale的情况\n */\nfunction normalizeUrl(pathname: string): { normalized: string; needsRedirect: boolean } {\n  console.log(`🔧 Normalizing URL: ${pathname}`);\n\n  // 移除开头和结尾的斜杠，然后分割路径\n  const segments = pathname.replace(/^\\/+|\\/+$/g, '').split('/').filter(Boolean);\n\n  // 如果路径为空，返回默认语言\n  if (segments.length === 0) {\n    return { normalized: `/${defaultLocale}`, needsRedirect: true };\n  }\n\n  const firstSegment = segments[0];\n\n  // 检查是否有重复的locale前缀\n  if (locales.includes(firstSegment)) {\n    const secondSegment = segments[1];\n\n    // 如果第二个段也是locale，说明有重复\n    if (secondSegment && locales.includes(secondSegment)) {\n      // 移除第一个重复的locale，保留第二个\n      const cleanedSegments = segments.slice(1);\n      const normalized = '/' + cleanedSegments.join('/');\n      console.log(`🔄 Found duplicate locale: ${pathname} -> ${normalized}`);\n      return { normalized, needsRedirect: true };\n    }\n\n    // 如果只有一个locale前缀，路径已经正确\n    return { normalized: pathname, needsRedirect: false };\n  }\n\n  // 如果没有locale前缀，添加默认locale\n  const normalized = `/${defaultLocale}/${segments.join('/')}`;\n  console.log(`🔄 Adding default locale: ${pathname} -> ${normalized}`);\n  return { normalized, needsRedirect: true };\n}\n\n// 自定义URL规范化中间件\nfunction urlNormalizationMiddleware(request: NextRequest, response: NextResponse) {\n  const { pathname, search } = request.nextUrl;\n\n  console.log(`🔍 URL normalization middleware processing: ${pathname}`);\n\n  // 跳过API路由和静态资源\n  if (\n    pathname.startsWith('/api') ||\n    pathname.startsWith('/_next') ||\n    pathname.includes('.') ||\n    pathname === '/favicon.ico' ||\n    pathname === '/robots.txt' ||\n    pathname === '/sitemap.xml' ||\n    pathname.startsWith('/uploads')\n  ) {\n    console.log(`⏭️ Skipping static resource: ${pathname}`);\n    return response; // 返回原始响应\n  }\n\n  // 规范化URL\n  const { normalized, needsRedirect } = normalizeUrl(pathname);\n\n  // 如果需要重定向，执行重定向\n  if (needsRedirect) {\n    const redirectUrl = new URL(normalized + search, request.url);\n    console.log(`🔄 URL Redirect: ${pathname} -> ${normalized}`);\n    return NextResponse.redirect(redirectUrl, 307);\n  }\n\n  // 如果URL已经规范化，返回原始响应\n  console.log(`✅ URL is valid, returning response`);\n  return response;\n}\n\n// 主中间件：先运行 next-intl，然后运行自定义逻辑\nexport default function middleware(request: NextRequest) {\n  console.log(`🚀 Main middleware called: ${request.nextUrl.pathname}`);\n\n  // 首先运行 next-intl 中间件\n  const i18nResponse = i18nMiddleware(request);\n\n  // 如果 next-intl 返回了重定向，直接返回\n  if (i18nResponse && !i18nResponse.ok) {\n    console.log(`🔄 next-intl middleware returned redirect, status: ${i18nResponse.status}`);\n    return i18nResponse;\n  }\n\n  // 否则运行自定义URL规范化中间件\n  console.log(`✅ Running custom URL normalization middleware`);\n  return urlNormalizationMiddleware(request, i18nResponse || NextResponse.next());\n}\n\nexport const config = {\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEA,UAAU;AACV,MAAM,UAAU;IAAC;IAAM;CAAK;AAC5B,MAAM,gBAAgB;AAEtB,mBAAmB;AACnB,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,8IAAA,CAAA,UAAO;AAE/C;;CAEC,GACD,SAAS,aAAa,QAAgB;IACpC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;IAE7C,oBAAoB;IACpB,MAAM,WAAW,SAAS,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC;IAEtE,gBAAgB;IAChB,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;YAAE,YAAY,CAAC,CAAC,EAAE,eAAe;YAAE,eAAe;QAAK;IAChE;IAEA,MAAM,eAAe,QAAQ,CAAC,EAAE;IAEhC,mBAAmB;IACnB,IAAI,QAAQ,QAAQ,CAAC,eAAe;QAClC,MAAM,gBAAgB,QAAQ,CAAC,EAAE;QAEjC,uBAAuB;QACvB,IAAI,iBAAiB,QAAQ,QAAQ,CAAC,gBAAgB;YACpD,uBAAuB;YACvB,MAAM,kBAAkB,SAAS,KAAK,CAAC;YACvC,MAAM,aAAa,MAAM,gBAAgB,IAAI,CAAC;YAC9C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,SAAS,IAAI,EAAE,YAAY;YACrE,OAAO;gBAAE;gBAAY,eAAe;YAAK;QAC3C;QAEA,wBAAwB;QACxB,OAAO;YAAE,YAAY;YAAU,eAAe;QAAM;IACtD;IAEA,0BAA0B;IAC1B,MAAM,aAAa,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,SAAS,IAAI,CAAC,MAAM;IAC5D,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,SAAS,IAAI,EAAE,YAAY;IACpE,OAAO;QAAE;QAAY,eAAe;IAAK;AAC3C;AAEA,eAAe;AACf,SAAS,2BAA2B,OAAoB,EAAE,QAAsB;IAC9E,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;IAE5C,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,UAAU;IAErE,eAAe;IACf,IACE,SAAS,UAAU,CAAC,WACpB,SAAS,UAAU,CAAC,aACpB,SAAS,QAAQ,CAAC,QAClB,aAAa,kBACb,aAAa,iBACb,aAAa,kBACb,SAAS,UAAU,CAAC,aACpB;QACA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,UAAU;QACtD,OAAO,UAAU,SAAS;IAC5B;IAEA,SAAS;IACT,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,aAAa;IAEnD,gBAAgB;IAChB,IAAI,eAAe;QACjB,MAAM,cAAc,IAAI,IAAI,aAAa,QAAQ,QAAQ,GAAG;QAC5D,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,IAAI,EAAE,YAAY;QAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,aAAa;IAC5C;IAEA,oBAAoB;IACpB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC;IAChD,OAAO;AACT;AAGe,SAAS,WAAW,OAAoB;IACrD,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;IAEpE,qBAAqB;IACrB,MAAM,eAAe,eAAe;IAEpC,2BAA2B;IAC3B,IAAI,gBAAgB,CAAC,aAAa,EAAE,EAAE;QACpC,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,aAAa,MAAM,EAAE;QACvF,OAAO;IACT;IAEA,mBAAmB;IACnB,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC;IAC3D,OAAO,2BAA2B,SAAS,gBAAgB,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC9E;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}