{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport {\n  useStripe,\n  useElements,\n  PaymentElement,\n  AddressElement\n} from '@stripe/react-stripe-js';\nimport { CreditCard, Loader2 } from 'lucide-react';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { Locale } from '@/i18n/config';\n\ninterface StripeCheckoutFormProps {\n  onSuccess: () => void;\n  amount: number;\n}\n\nexport default function StripeCheckoutForm({ onSuccess, amount }: StripeCheckoutFormProps) {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  const pathname = usePathname();\n  const t = useTranslations('payment');\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    setIsProcessing(true);\n    setErrorMessage('');\n\n    try {\n      // 确认支付\n      const { error } = await stripe.confirmPayment({\n        elements,\n        confirmParams: {\n          return_url: `${window.location.origin}/${currentLocale}/submit/success`,\n        },\n        redirect: 'if_required'\n      });\n\n      if (error) {\n        // 支付失败\n        if (error.type === 'card_error' || error.type === 'validation_error') {\n          setErrorMessage(error.message || t('payment_failed'));\n        } else {\n          setErrorMessage(t('payment_error'));\n        }\n      } else {\n        // 支付成功\n        onSuccess();\n      }\n    } catch (err) {\n      setErrorMessage(t('payment_processing_failed'));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 支付方式选择 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('payment_method')}\n        </label>\n        <PaymentElement \n          options={{\n            layout: 'tabs',\n            defaultValues: {\n              billingDetails: {\n                address: {\n                  country: 'CN'\n                }\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 账单地址 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('billing_address')}\n        </label>\n        <AddressElement \n          options={{\n            mode: 'billing',\n            defaultValues: {\n              address: {\n                country: 'CN'\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 错误信息 */}\n      {errorMessage && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-600 text-sm\">{errorMessage}</p>\n        </div>\n      )}\n\n      {/* 支付按钮 */}\n      <button\n        type=\"submit\"\n        disabled={!stripe || !elements || isProcessing}\n        className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors\"\n      >\n        {isProcessing ? (\n          <>\n            <Loader2 className=\"h-5 w-5 mr-2 animate-spin\" />\n            {t('processing_payment')}\n          </>\n        ) : (\n          <>\n            <CreditCard className=\"h-5 w-5 mr-2\" />\n            {t('pay_now', { amount: formatStripeAmount(amount) })}\n          </>\n        )}\n      </button>\n\n      {/* 安全提示 */}\n      <div className=\"text-center\">\n        <p className=\"text-gray-500 text-xs\">\n          {t('security_notice')}\n        </p>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AACA;;;AAZA;;;;;;;AAoBe,SAAS,mBAAmB,EAAE,SAAS,EAAE,MAAM,EAA2B;;IACvF,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,OAAO;YACP,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,cAAc,CAAC;gBAC5C;gBACA,eAAe;oBACb,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,eAAe,CAAC;gBACzE;gBACA,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,OAAO;gBACP,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,oBAAoB;oBACpE,gBAAgB,MAAM,OAAO,IAAI,EAAE;gBACrC,OAAO;oBACL,gBAAgB,EAAE;gBACpB;YACF,OAAO;gBACL,OAAO;gBACP;YACF;QACF,EAAE,OAAO,KAAK;YACZ,gBAAgB,EAAE;QACpB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,6LAAC,sLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,QAAQ;4BACR,eAAe;gCACb,gBAAgB;oCACd,SAAS;wCACP,SAAS;oCACX;gCACF;4BACF;wBACF;;;;;;;;;;;;0BAKJ,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,6LAAC,sLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,MAAM;4BACN,eAAe;gCACb,SAAS;oCACP,SAAS;gCACX;4BACF;wBACF;;;;;;;;;;;;YAKH,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAKzC,6LAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU,CAAC,YAAY;gBAClC,WAAU;0BAET,6BACC;;sCACE,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;iDAGL;;sCACE,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBACrB,EAAE,WAAW;4BAAE,QAAQ,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE;wBAAQ;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;;;;;;;;;;;;AAKb;GA1HwB;;QACP,sLAAA,CAAA,YAAS;QACP,sLAAA,CAAA,cAAW;QAIX,yHAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;;;KAPH", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AAGK;AAFC,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,uCAAmC;;IAwBnC;IAEA,aAAa;IACb,wCAAmC;QACjC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO,QAAQ;QACpD,OAAO,GAAG,SAAS,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI;IAC5D;;AAIF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,uCAAmC;;IAEnC;IAEA,MAAM;IACN,OAAO,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,KAAK,WAAW,QAAQ,IAAI;AACtF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,aAAkB,aAAa;;AAOtD", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n\n  // 订单API\n  async getOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}`);\n  }\n\n  async processOrderPayment(id: string, data: {\n    paymentMethod?: string;\n  }): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}/pay`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;IAEA,QAAQ;IACR,MAAM,SAAS,EAAU,EAA6B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,IAAI;IAC1C;IAEA,MAAM,oBAAoB,EAAU,EAAE,IAErC,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/payment/checkout/CheckoutClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from '@/i18n/routing';\nimport { loadStripe } from '@stripe/stripe-js';\nimport { Elements } from '@stripe/react-stripe-js';\nimport StripeCheckoutForm from '@/components/StripeCheckoutForm';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';\nimport { apiClient } from '@/lib/api';\nimport { useTranslations } from 'next-intl';\n\n// 初始化Stripe\nconst stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);\n\ninterface OrderData {\n  _id: string;\n  type: string;\n  amount: number;\n  currency: string;\n  status: string;\n  description: string;\n  selectedLaunchDate: string | null;\n  createdAt: string | null;\n  paidAt: string | null;\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n  } | null;\n  toolId: string | null;\n}\n\ninterface CheckoutClientProps {\n  order: OrderData;\n  orderId: string;\n}\n\nexport default function CheckoutClient({ order, orderId }: CheckoutClientProps) {\n  const router = useRouter();\n  const [clientSecret, setClientSecret] = useState<string>('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n\n  // 获取翻译\n  const t = useTranslations('checkout');\n\n  useEffect(() => {\n    createPaymentIntent();\n  }, [orderId]);\n\n  const createPaymentIntent = async () => {\n    try {\n      const response = await fetch('/api/stripe/create-payment-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ orderId }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setClientSecret(data.data.clientSecret);\n      } else {\n        setError(data.message || t('payment_error_title'));\n      }\n    } catch (error) {\n      console.error('创建支付意图失败:', error);\n      setError(t('payment_error_title'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePaymentSuccess = async () => {\n    try {\n      // 调用订单支付接口确保状态更新\n      const response = await apiClient.processOrderPayment(orderId, {\n        paymentMethod: 'stripe'\n      });\n\n      if (response.success) {\n        console.log('订单状态更新成功:', response.data);\n      } else {\n        console.warn('订单状态更新失败:', response.error);\n        // 即使状态更新失败，也继续跳转，因为Webhook会处理\n      }\n    } catch (error) {\n      console.error('调用订单支付接口失败:', error);\n      // 即使接口调用失败，也继续跳转，因为Webhook会处理\n    }\n\n    // 使用当前语言环境进行路由跳转\n    router.push(`/submit/success?toolId=${order.toolId}&paid=true`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">{t('creating_payment_session')}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{t('payment_error_title')}</h1>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button\n            onClick={() => router.push('/submit')}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            {t('back_to_submit')}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"text-center mb-8\">\n        <CreditCard className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          {t('page_title')}\n        </h1>\n        <p className=\"text-lg text-gray-600\">\n          {t('page_subtitle')}\n        </p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6 mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('order_details')}</h2>\n\n        <div className=\"space-y-3\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('service_type')}</span>\n            <span className=\"font-medium\">{t('tool_priority_launch')}</span>\n          </div>\n\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('tool_name')}</span>\n            <span className=\"font-medium\">{order.tool?.name || t('loading_placeholder')}</span>\n          </div>\n\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('launch_date')}</span>\n            <span className=\"font-medium\">\n              {order.selectedLaunchDate ? new Date(order.selectedLaunchDate).toLocaleDateString() : t('loading_placeholder')}\n            </span>\n          </div>\n\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('order_number')}</span>\n            <span className=\"font-medium text-sm\">{order._id}</span>\n          </div>\n\n          <hr className=\"my-4\" />\n\n          <div className=\"flex justify-between text-lg font-semibold\">\n            <span>{t('total')}</span>\n            <span className=\"text-blue-600\">{formatStripeAmount(order.amount)}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Service Features */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">\n          {t('priority_service_title')}\n        </h3>\n        <ul className=\"space-y-2\">\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_any_date')}\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_priority_review')}\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_homepage_featured')}\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            {t('feature_dedicated_support')}\n          </li>\n        </ul>\n      </div>\n\n      {/* Security Notice */}\n      <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center text-gray-700\">\n          <Shield className=\"h-5 w-5 mr-2 text-green-500\" />\n          <span className=\"text-sm\">\n            {t('security_notice')}\n          </span>\n        </div>\n      </div>\n\n      {/* Stripe Payment Form */}\n      {clientSecret && (\n        <Elements\n          stripe={stripePromise}\n          options={{\n            clientSecret,\n            appearance: {\n              theme: 'stripe',\n              variables: {\n                colorPrimary: '#2563eb',\n              }\n            }\n          }}\n        >\n          <StripeCheckoutForm\n            onSuccess={handlePaymentSuccess}\n            amount={order.amount}\n          />\n        </Elements>\n      )}\n\n      <p className=\"text-gray-500 text-sm mt-4 text-center\">\n        {t('terms_notice')}\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAaiC;;AAXjC;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,YAAY;AACZ,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD;AAyBhB,SAAS,eAAe,EAAE,KAAK,EAAE,OAAO,EAAuB;;IAC5E,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,OAAO;IACP,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAQ;IAEZ,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI,CAAC,YAAY;YACxC,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,SAAS;gBAC5D,eAAe;YACjB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,QAAQ,GAAG,CAAC,aAAa,SAAS,IAAI;YACxC,OAAO;gBACL,QAAQ,IAAI,CAAC,aAAa,SAAS,KAAK;YACxC,8BAA8B;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,8BAA8B;QAChC;QAEA,iBAAiB;QACjB,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,MAAM,MAAM,CAAC,UAAU,CAAC;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAsB,EAAE;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAG,WAAU;kCAAyC,EAAE;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4C,EAAE;;;;;;kCAE5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAe,EAAE;;;;;;;;;;;;0CAGnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAe,MAAM,IAAI,EAAE,QAAQ,EAAE;;;;;;;;;;;;0CAGvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDACb,MAAM,kBAAkB,GAAG,IAAI,KAAK,MAAM,kBAAkB,EAAE,kBAAkB,KAAK,EAAE;;;;;;;;;;;;0CAI5F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAuB,MAAM,GAAG;;;;;;;;;;;;0CAGlD,6LAAC;gCAAG,WAAU;;;;;;0CAEd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAM,EAAE;;;;;;kDACT,6LAAC;wCAAK,WAAU;kDAAiB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;0CAEL,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;0CAEL,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;0CAEL,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;;;;;;;;;;;;;;;;;;0BAMT,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAK,WAAU;sCACb,EAAE;;;;;;;;;;;;;;;;;YAMR,8BACC,6LAAC,sLAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS;oBACP;oBACA,YAAY;wBACV,OAAO;wBACP,WAAW;4BACT,cAAc;wBAChB;oBACF;gBACF;0BAEA,cAAA,6LAAC,2IAAA,CAAA,UAAkB;oBACjB,WAAW;oBACX,QAAQ,MAAM,MAAM;;;;;;;;;;;0BAK1B,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;;;;;;;AAIX;GAvMwB;;QACP,yHAAA,CAAA,YAAS;QAMd,yMAAA,CAAA,kBAAe;;;KAPH", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/%40stripe/stripe-js/dist/index.mjs"], "sourcesContent": ["var RELEASE_TRAIN = 'basil';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"7.4.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise$1 !== null) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise$1[\"catch\"](function (error) {\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"7.4.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise) {\n    return stripePromise;\n  }\n\n  stripePromise = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n  return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\nexport { loadStripe };\n"], "names": [], "mappings": ";;;AAAA,IAAI,gBAAgB;AAEpB,IAAI,6BAA6B,SAAS,2BAA2B,OAAO;IAC1E,OAAO,YAAY,IAAI,OAAO;AAChC;AAEA,IAAI,SAAS;AACb,IAAI,gBAAgB,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,eAAe;AACjE,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,0BAA0B;AAE9B,IAAI,gBAAgB,SAAS,cAAc,GAAG;IAC5C,OAAO,aAAa,IAAI,CAAC,QAAQ,oBAAoB,IAAI,CAAC;AAC5D;AAEA,IAAI,aAAa,SAAS;IACxB,IAAI,UAAU,SAAS,gBAAgB,CAAC,iBAAiB,MAAM,CAAC,QAAQ;IAExE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,SAAS,OAAO,CAAC,EAAE;QAEvB,IAAI,CAAC,cAAc,OAAO,GAAG,GAAG;YAC9B;QACF;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,IAAI,eAAe,SAAS,aAAa,MAAM;IAC7C,IAAI,cAAc,UAAU,CAAC,OAAO,oBAAoB,GAAG,gCAAgC;IAC3F,IAAI,SAAS,SAAS,aAAa,CAAC;IACpC,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC,eAAe,MAAM,CAAC;IAC7C,IAAI,aAAa,SAAS,IAAI,IAAI,SAAS,IAAI;IAE/C,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,WAAW,WAAW,CAAC;IACvB,OAAO;AACT;AAEA,IAAI,kBAAkB,SAAS,gBAAgB,MAAM,EAAE,SAAS;IAC9D,IAAI,CAAC,UAAU,CAAC,OAAO,gBAAgB,EAAE;QACvC;IACF;IAEA,OAAO,gBAAgB,CAAC;QACtB,MAAM;QACN,SAAS;QACT,WAAW;IACb;AACF;AAEA,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AAErB,IAAI,UAAU,SAAS,QAAQ,MAAM;IACnC,OAAO,SAAU,KAAK;QACpB,OAAO,IAAI,MAAM,4BAA4B;YAC3C,OAAO;QACT;IACF;AACF;AAEA,IAAI,SAAS,SAAS,OAAO,OAAO,EAAE,MAAM;IAC1C,OAAO;QACL,IAAI,OAAO,MAAM,EAAE;YACjB,QAAQ,OAAO,MAAM;QACvB,OAAO;YACL,OAAO,IAAI,MAAM;QACnB;IACF;AACF;AAEA,IAAI,aAAa,SAAS,WAAW,MAAM;IACzC,6DAA6D;IAC7D,IAAI,oBAAoB,MAAM;QAC5B,OAAO;IACT;IAEA,kBAAkB,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QACrD,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,aAAa;YACpE,mEAAmE;YACnE,6CAA6C;YAC7C,QAAQ;YACR;QACF;QAEA,IAAI,OAAO,MAAM,IAAI,QAAQ;YAC3B,QAAQ,IAAI,CAAC;QACf;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,QAAQ,OAAO,MAAM;YACrB;QACF;QAEA,IAAI;YACF,IAAI,SAAS;YAEb,IAAI,UAAU,QAAQ;gBACpB,QAAQ,IAAI,CAAC;YACf,OAAO,IAAI,CAAC,QAAQ;gBAClB,SAAS,aAAa;YACxB,OAAO,IAAI,UAAU,mBAAmB,QAAQ,oBAAoB,MAAM;gBACxE,IAAI;gBAEJ,yBAAyB;gBACzB,OAAO,mBAAmB,CAAC,QAAQ;gBACnC,OAAO,mBAAmB,CAAC,SAAS,kBAAkB,0DAA0D;gBAChH,wCAAwC;gBAExC,CAAC,qBAAqB,OAAO,UAAU,MAAM,QAAQ,uBAAuB,KAAK,IAAI,KAAK,IAAI,mBAAmB,WAAW,CAAC;gBAC7H,SAAS,aAAa;YACxB;YAEA,iBAAiB,OAAO,SAAS;YACjC,kBAAkB,QAAQ;YAC1B,OAAO,gBAAgB,CAAC,QAAQ;YAChC,OAAO,gBAAgB,CAAC,SAAS;QACnC,EAAE,OAAO,OAAO;YACd,OAAO;YACP;QACF;IACF,IAAI,gCAAgC;IAEpC,OAAO,eAAe,CAAC,QAAQ,CAAC,SAAU,KAAK;QAC7C,kBAAkB;QAClB,OAAO,QAAQ,MAAM,CAAC;IACxB;AACF;AACA,IAAI,aAAa,SAAS,WAAW,WAAW,EAAE,IAAI,EAAE,SAAS;IAC/D,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IAEA,IAAI,KAAK,IAAI,CAAC,EAAE;IAChB,IAAI,YAAY,GAAG,KAAK,CAAC,aAAa,8CAA8C;IAEpF,IAAI,UAAU,2BAA2B,YAAY,OAAO;IAC5D,IAAI,kBAAkB;IAEtB,IAAI,aAAa,YAAY,iBAAiB;QAC5C,QAAQ,IAAI,CAAC,aAAa,MAAM,CAAC,SAAS,mDAAmD,MAAM,CAAC,SAAS,wBAAwB,MAAM,CAAC,iBAAiB;IAC/J;IAEA,IAAI,SAAS,YAAY,KAAK,CAAC,WAAW;IAC1C,gBAAgB,QAAQ;IACxB,OAAO;AACT,GAAG,6EAA6E;AAEhF,IAAI;AACJ,IAAI,aAAa;AAEjB,IAAI,mBAAmB,SAAS;IAC9B,IAAI,eAAe;QACjB,OAAO;IACT;IAEA,gBAAgB,WAAW,KAAK,CAAC,QAAQ,CAAC,SAAU,KAAK;QACvD,uBAAuB;QACvB,gBAAgB;QAChB,OAAO,QAAQ,MAAM,CAAC;IACxB;IACA,OAAO;AACT,GAAG,+EAA+E;AAClF,wBAAwB;AAGxB,QAAQ,OAAO,GAAG,IAAI,CAAC;IACrB,OAAO;AACT,EAAE,CAAC,QAAQ,CAAC,SAAU,KAAK;IACzB,IAAI,CAAC,YAAY;QACf,QAAQ,IAAI,CAAC;IACf;AACF;AACA,IAAI,aAAa,SAAS;IACxB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC9B;IAEA,aAAa;IACb,IAAI,YAAY,KAAK,GAAG,IAAI,6DAA6D;IAEzF,OAAO,mBAAmB,IAAI,CAAC,SAAU,WAAW;QAClD,OAAO,WAAW,aAAa,MAAM;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/%40stripe/stripe-js/lib/index.mjs"], "sourcesContent": ["export * from '../dist/index.mjs';\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/%40stripe/react-stripe-js/dist/react-stripe.umd.js"], "sourcesContent": ["(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('react')) :\n  typeof define === 'function' && define.amd ? define(['exports', 'react'], factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ReactStripe = {}, global.React));\n})(this, (function (exports, React) { 'use strict';\n\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n\n      keys.push.apply(keys, symbols);\n    }\n\n    return keys;\n  }\n\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n\n    return target;\n  }\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n\n    return obj;\n  }\n\n  function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n\n    return target;\n  }\n\n  function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n\n    var key, i;\n\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n\n    return target;\n  }\n\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n\n  function _iterableToArrayLimit(arr, i) {\n    var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n\n    var _s, _e;\n\n    try {\n      for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  function getDefaultExportFromCjs (x) {\n  \treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n  }\n\n  var propTypes = {exports: {}};\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var ReactPropTypesSecret_1;\n  var hasRequiredReactPropTypesSecret;\n\n  function requireReactPropTypesSecret() {\n    if (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;\n    hasRequiredReactPropTypesSecret = 1;\n\n    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n    ReactPropTypesSecret_1 = ReactPropTypesSecret;\n    return ReactPropTypesSecret_1;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var factoryWithThrowingShims;\n  var hasRequiredFactoryWithThrowingShims;\n\n  function requireFactoryWithThrowingShims() {\n    if (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;\n    hasRequiredFactoryWithThrowingShims = 1;\n\n    var ReactPropTypesSecret = requireReactPropTypesSecret();\n\n    function emptyFunction() {}\n\n    function emptyFunctionWithReset() {}\n\n    emptyFunctionWithReset.resetWarningCache = emptyFunction;\n\n    factoryWithThrowingShims = function () {\n      function shim(props, propName, componentName, location, propFullName, secret) {\n        if (secret === ReactPropTypesSecret) {\n          // It is still safe when called from React.\n          return;\n        }\n\n        var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n        err.name = 'Invariant Violation';\n        throw err;\n      }\n      shim.isRequired = shim;\n\n      function getShim() {\n        return shim;\n      }\n      // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\n      var ReactPropTypes = {\n        array: shim,\n        bool: shim,\n        func: shim,\n        number: shim,\n        object: shim,\n        string: shim,\n        symbol: shim,\n        any: shim,\n        arrayOf: getShim,\n        element: shim,\n        elementType: shim,\n        instanceOf: getShim,\n        node: shim,\n        objectOf: getShim,\n        oneOf: getShim,\n        oneOfType: getShim,\n        shape: getShim,\n        exact: getShim,\n        checkPropTypes: emptyFunctionWithReset,\n        resetWarningCache: emptyFunction\n      };\n      ReactPropTypes.PropTypes = ReactPropTypes;\n      return ReactPropTypes;\n    };\n\n    return factoryWithThrowingShims;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  {\n    // By explicitly using `prop-types` you are opting into new production behavior.\n    // http://fb.me/prop-types-in-prod\n    propTypes.exports = requireFactoryWithThrowingShims()();\n  }\n\n  var propTypesExports = propTypes.exports;\n  var PropTypes = /*@__PURE__*/getDefaultExportFromCjs(propTypesExports);\n\n  var useAttachEvent = function useAttachEvent(element, event, cb) {\n    var cbDefined = !!cb;\n    var cbRef = React.useRef(cb); // In many integrations the callback prop changes on each render.\n    // Using a ref saves us from calling element.on/.off every render.\n\n    React.useEffect(function () {\n      cbRef.current = cb;\n    }, [cb]);\n    React.useEffect(function () {\n      if (!cbDefined || !element) {\n        return function () {};\n      }\n\n      var decoratedCb = function decoratedCb() {\n        if (cbRef.current) {\n          cbRef.current.apply(cbRef, arguments);\n        }\n      };\n\n      element.on(event, decoratedCb);\n      return function () {\n        element.off(event, decoratedCb);\n      };\n    }, [cbDefined, event, element, cbRef]);\n  };\n\n  var usePrevious = function usePrevious(value) {\n    var ref = React.useRef(value);\n    React.useEffect(function () {\n      ref.current = value;\n    }, [value]);\n    return ref.current;\n  };\n\n  var isUnknownObject = function isUnknownObject(raw) {\n    return raw !== null && _typeof(raw) === 'object';\n  };\n  var isPromise = function isPromise(raw) {\n    return isUnknownObject(raw) && typeof raw.then === 'function';\n  }; // We are using types to enforce the `stripe` prop in this lib,\n  // but in an untyped integration `stripe` could be anything, so we need\n  // to do some sanity validation to prevent type errors.\n\n  var isStripe = function isStripe(raw) {\n    return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n  };\n\n  var PLAIN_OBJECT_STR = '[object Object]';\n  var isEqual = function isEqual(left, right) {\n    if (!isUnknownObject(left) || !isUnknownObject(right)) {\n      return left === right;\n    }\n\n    var leftArray = Array.isArray(left);\n    var rightArray = Array.isArray(right);\n    if (leftArray !== rightArray) return false;\n    var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n    var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n    if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n    // fallback to reference check.\n\n    if (!leftPlainObject && !leftArray) return left === right;\n    var leftKeys = Object.keys(left);\n    var rightKeys = Object.keys(right);\n    if (leftKeys.length !== rightKeys.length) return false;\n    var keySet = {};\n\n    for (var i = 0; i < leftKeys.length; i += 1) {\n      keySet[leftKeys[i]] = true;\n    }\n\n    for (var _i = 0; _i < rightKeys.length; _i += 1) {\n      keySet[rightKeys[_i]] = true;\n    }\n\n    var allKeys = Object.keys(keySet);\n\n    if (allKeys.length !== leftKeys.length) {\n      return false;\n    }\n\n    var l = left;\n    var r = right;\n\n    var pred = function pred(key) {\n      return isEqual(l[key], r[key]);\n    };\n\n    return allKeys.every(pred);\n  };\n\n  var extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n    if (!isUnknownObject(options)) {\n      return null;\n    }\n\n    return Object.keys(options).reduce(function (newOptions, key) {\n      var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n      if (immutableKeys.includes(key)) {\n        if (isUpdated) {\n          console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n        }\n\n        return newOptions;\n      }\n\n      if (!isUpdated) {\n        return newOptions;\n      }\n\n      return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n    }, null);\n  };\n\n  var INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n  // integration `stripe` could be anything, so we need to do some sanity\n  // validation to prevent type errors.\n\n  var validateStripe = function validateStripe(maybeStripe) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (maybeStripe === null || isStripe(maybeStripe)) {\n      return maybeStripe;\n    }\n\n    throw new Error(errorMsg);\n  };\n\n  var parseStripeProp = function parseStripeProp(raw) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (isPromise(raw)) {\n      return {\n        tag: 'async',\n        stripePromise: Promise.resolve(raw).then(function (result) {\n          return validateStripe(result, errorMsg);\n        })\n      };\n    }\n\n    var stripe = validateStripe(raw, errorMsg);\n\n    if (stripe === null) {\n      return {\n        tag: 'empty'\n      };\n    }\n\n    return {\n      tag: 'sync',\n      stripe: stripe\n    };\n  };\n\n  var registerWithStripeJs = function registerWithStripeJs(stripe) {\n    if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n      return;\n    }\n\n    stripe._registerWrapper({\n      name: 'react-stripe-js',\n      version: \"3.7.0\"\n    });\n\n    stripe.registerAppInfo({\n      name: 'react-stripe-js',\n      version: \"3.7.0\",\n      url: 'https://stripe.com/docs/stripe-js/react'\n    });\n  };\n\n  var ElementsContext = /*#__PURE__*/React.createContext(null);\n  ElementsContext.displayName = 'ElementsContext';\n  var parseElementsContext = function parseElementsContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n    }\n\n    return ctx;\n  };\n  /**\n   * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n   * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n   *\n   * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n   * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n   * Pass the returned `Promise` to `Elements`.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n   */\n\n  var Elements = function Elements(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp);\n    }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n    var _React$useState = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n      };\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      var isMounted = true;\n\n      var safeSetContext = function safeSetContext(stripe) {\n        setContext(function (ctx) {\n          // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n          if (ctx.stripe) return ctx;\n          return {\n            stripe: stripe,\n            elements: stripe.elements(options)\n          };\n        });\n      }; // For an async stripePromise, store it in context once resolved\n\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted) {\n            // Only update Elements context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            safeSetContext(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !ctx.stripe) {\n        // Or, handle a sync stripe instance going from null -> populated\n        safeSetContext(parsed.stripe);\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (!ctx.elements) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n      if (updates) {\n        ctx.elements.update(updates);\n      }\n    }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    return /*#__PURE__*/React.createElement(ElementsContext.Provider, {\n      value: ctx\n    }, children);\n  };\n  Elements.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.object\n  };\n  var useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n    var ctx = React.useContext(ElementsContext);\n    return parseElementsContext(ctx, useCaseMessage);\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n   */\n\n  var useElements = function useElements() {\n    var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n        elements = _useElementsContextWi.elements;\n\n    return elements;\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n   */\n\n  var ElementsConsumer = function ElementsConsumer(_ref2) {\n    var children = _ref2.children;\n    var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n    return children(ctx);\n  };\n  ElementsConsumer.propTypes = {\n    children: PropTypes.func.isRequired\n  };\n\n  var _excluded$1 = [\"on\", \"session\"];\n  var CheckoutSdkContext = /*#__PURE__*/React.createContext(null);\n  CheckoutSdkContext.displayName = 'CheckoutSdkContext';\n  var parseCheckoutSdkContext = function parseCheckoutSdkContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find CheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CheckoutProvider> provider.\"));\n    }\n\n    return ctx;\n  };\n  var CheckoutContext = /*#__PURE__*/React.createContext(null);\n  CheckoutContext.displayName = 'CheckoutContext';\n  var extractCheckoutContextValue = function extractCheckoutContextValue(checkoutSdk, sessionState) {\n    if (!checkoutSdk) {\n      return null;\n    }\n\n    checkoutSdk.on;\n        checkoutSdk.session;\n        var actions = _objectWithoutProperties(checkoutSdk, _excluded$1);\n\n    if (!sessionState) {\n      return Object.assign(checkoutSdk.session(), actions);\n    }\n\n    return Object.assign(sessionState, actions);\n  };\n  var INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var CheckoutProvider = function CheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n    }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n    var _React$useState = React.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        session = _React$useState2[0],\n        setSession = _React$useState2[1];\n\n    var _React$useState3 = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        checkoutSdk: null\n      };\n    }),\n        _React$useState4 = _slicedToArray(_React$useState3, 2),\n        ctx = _React$useState4[0],\n        setContext = _React$useState4[1];\n\n    var safeSetContext = function safeSetContext(stripe, checkoutSdk) {\n      setContext(function (ctx) {\n        if (ctx.stripe && ctx.checkoutSdk) {\n          return ctx;\n        }\n\n        return {\n          stripe: stripe,\n          checkoutSdk: checkoutSdk\n        };\n      });\n    }; // Ref used to avoid calling initCheckout multiple times when options changes\n\n\n    var initCheckoutCalledRef = React.useRef(false);\n    React.useEffect(function () {\n      var isMounted = true;\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted && !initCheckoutCalledRef.current) {\n            // Only update context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            initCheckoutCalledRef.current = true;\n            stripe.initCheckout(options).then(function (checkoutSdk) {\n              if (checkoutSdk) {\n                safeSetContext(stripe, checkoutSdk);\n                checkoutSdk.on('change', setSession);\n              }\n            });\n          }\n        });\n      } else if (parsed.tag === 'sync' && parsed.stripe && !initCheckoutCalledRef.current) {\n        initCheckoutCalledRef.current = true;\n        parsed.stripe.initCheckout(options).then(function (checkoutSdk) {\n          if (checkoutSdk) {\n            safeSetContext(parsed.stripe, checkoutSdk);\n            checkoutSdk.on('change', setSession);\n          }\n        });\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    var prevCheckoutSdk = usePrevious(ctx.checkoutSdk);\n    React.useEffect(function () {\n      var _prevOptions$elements, _options$elementsOpti;\n\n      // Ignore changes while checkout sdk is not initialized.\n      if (!ctx.checkoutSdk) {\n        return;\n      }\n\n      var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n      var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n      var hasAppearanceChanged = !isEqual(currentAppearance, previousAppearance);\n      var hasSdkLoaded = !prevCheckoutSdk && ctx.checkoutSdk;\n\n      if (currentAppearance && (hasAppearanceChanged || hasSdkLoaded)) {\n        ctx.checkoutSdk.changeAppearance(currentAppearance);\n      }\n    }, [options, prevOptions, ctx.checkoutSdk, prevCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    var checkoutContextValue = React.useMemo(function () {\n      return extractCheckoutContextValue(ctx.checkoutSdk, session);\n    }, [ctx.checkoutSdk, session]);\n\n    if (!ctx.checkoutSdk) {\n      return null;\n    }\n\n    return /*#__PURE__*/React.createElement(CheckoutSdkContext.Provider, {\n      value: ctx\n    }, /*#__PURE__*/React.createElement(CheckoutContext.Provider, {\n      value: checkoutContextValue\n    }, children));\n  };\n  CheckoutProvider.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.shape({\n      fetchClientSecret: PropTypes.func.isRequired,\n      elementsOptions: PropTypes.object\n    }).isRequired\n  };\n  var useCheckoutSdkContextWithUseCase = function useCheckoutSdkContextWithUseCase(useCaseString) {\n    var ctx = React.useContext(CheckoutSdkContext);\n    return parseCheckoutSdkContext(ctx, useCaseString);\n  };\n  var useElementsOrCheckoutSdkContextWithUseCase = function useElementsOrCheckoutSdkContextWithUseCase(useCaseString) {\n    var checkoutSdkContext = React.useContext(CheckoutSdkContext);\n    var elementsContext = React.useContext(ElementsContext);\n\n    if (checkoutSdkContext && elementsContext) {\n      throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CheckoutProvider> and <Elements> providers.\"));\n    }\n\n    if (checkoutSdkContext) {\n      return parseCheckoutSdkContext(checkoutSdkContext, useCaseString);\n    }\n\n    return parseElementsContext(elementsContext, useCaseString);\n  };\n  var useCheckout = function useCheckout() {\n    // ensure it's in CheckoutProvider\n    useCheckoutSdkContextWithUseCase('calls useCheckout()');\n    var ctx = React.useContext(CheckoutContext);\n\n    if (!ctx) {\n      throw new Error('Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.');\n    }\n\n    return ctx;\n  };\n\n  var _excluded = [\"mode\"];\n\n  var capitalized = function capitalized(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  };\n\n  var createElementComponent = function createElementComponent(type, isServer) {\n    var displayName = \"\".concat(capitalized(type), \"Element\");\n\n    var ClientElement = function ClientElement(_ref) {\n      var id = _ref.id,\n          className = _ref.className,\n          _ref$options = _ref.options,\n          options = _ref$options === void 0 ? {} : _ref$options,\n          onBlur = _ref.onBlur,\n          onFocus = _ref.onFocus,\n          onReady = _ref.onReady,\n          onChange = _ref.onChange,\n          onEscape = _ref.onEscape,\n          onClick = _ref.onClick,\n          onLoadError = _ref.onLoadError,\n          onLoaderStart = _ref.onLoaderStart,\n          onNetworksChange = _ref.onNetworksChange,\n          onConfirm = _ref.onConfirm,\n          onCancel = _ref.onCancel,\n          onShippingAddressChange = _ref.onShippingAddressChange,\n          onShippingRateChange = _ref.onShippingRateChange;\n      var ctx = useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var elements = 'elements' in ctx ? ctx.elements : null;\n      var checkoutSdk = 'checkoutSdk' in ctx ? ctx.checkoutSdk : null;\n\n      var _React$useState = React.useState(null),\n          _React$useState2 = _slicedToArray(_React$useState, 2),\n          element = _React$useState2[0],\n          setElement = _React$useState2[1];\n\n      var elementRef = React.useRef(null);\n      var domNode = React.useRef(null); // For every event where the merchant provides a callback, call element.on\n      // with that callback. If the merchant ever changes the callback, removes\n      // the old callback with element.off and then call element.on with the new one.\n\n      useAttachEvent(element, 'blur', onBlur);\n      useAttachEvent(element, 'focus', onFocus);\n      useAttachEvent(element, 'escape', onEscape);\n      useAttachEvent(element, 'click', onClick);\n      useAttachEvent(element, 'loaderror', onLoadError);\n      useAttachEvent(element, 'loaderstart', onLoaderStart);\n      useAttachEvent(element, 'networkschange', onNetworksChange);\n      useAttachEvent(element, 'confirm', onConfirm);\n      useAttachEvent(element, 'cancel', onCancel);\n      useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n      useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n      useAttachEvent(element, 'change', onChange);\n      var readyCallback;\n\n      if (onReady) {\n        if (type === 'expressCheckout') {\n          // Passes through the event, which includes visible PM types\n          readyCallback = onReady;\n        } else {\n          // For other Elements, pass through the Element itself.\n          readyCallback = function readyCallback() {\n            onReady(element);\n          };\n        }\n      }\n\n      useAttachEvent(element, 'ready', readyCallback);\n      React.useLayoutEffect(function () {\n        if (elementRef.current === null && domNode.current !== null && (elements || checkoutSdk)) {\n          var newElement = null;\n\n          if (checkoutSdk) {\n            switch (type) {\n              case 'payment':\n                newElement = checkoutSdk.createPaymentElement(options);\n                break;\n\n              case 'address':\n                if ('mode' in options) {\n                  var mode = options.mode,\n                      restOptions = _objectWithoutProperties(options, _excluded);\n\n                  if (mode === 'shipping') {\n                    newElement = checkoutSdk.createShippingAddressElement(restOptions);\n                  } else if (mode === 'billing') {\n                    newElement = checkoutSdk.createBillingAddressElement(restOptions);\n                  } else {\n                    throw new Error(\"Invalid options.mode. mode must be 'billing' or 'shipping'.\");\n                  }\n                } else {\n                  throw new Error(\"You must supply options.mode. mode must be 'billing' or 'shipping'.\");\n                }\n\n                break;\n\n              case 'expressCheckout':\n                newElement = checkoutSdk.createExpressCheckoutElement(options);\n                break;\n\n              case 'currencySelector':\n                newElement = checkoutSdk.createCurrencySelectorElement();\n                break;\n\n              default:\n                throw new Error(\"Invalid Element type \".concat(displayName, \". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />.\"));\n            }\n          } else if (elements) {\n            newElement = elements.create(type, options);\n          } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n          elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n          setElement(newElement);\n\n          if (newElement) {\n            newElement.mount(domNode.current);\n          }\n        }\n      }, [elements, checkoutSdk, options]);\n      var prevOptions = usePrevious(options);\n      React.useEffect(function () {\n        if (!elementRef.current) {\n          return;\n        }\n\n        var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n        if (updates && 'update' in elementRef.current) {\n          elementRef.current.update(updates);\n        }\n      }, [options, prevOptions]);\n      React.useLayoutEffect(function () {\n        return function () {\n          if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n            try {\n              elementRef.current.destroy();\n              elementRef.current = null;\n            } catch (error) {// Do nothing\n            }\n          }\n        };\n      }, []);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className,\n        ref: domNode\n      });\n    }; // Only render the Element wrapper in a server environment.\n\n\n    var ServerElement = function ServerElement(props) {\n      useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var id = props.id,\n          className = props.className;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className\n      });\n    };\n\n    var Element = isServer ? ServerElement : ClientElement;\n    Element.propTypes = {\n      id: PropTypes.string,\n      className: PropTypes.string,\n      onChange: PropTypes.func,\n      onBlur: PropTypes.func,\n      onFocus: PropTypes.func,\n      onReady: PropTypes.func,\n      onEscape: PropTypes.func,\n      onClick: PropTypes.func,\n      onLoadError: PropTypes.func,\n      onLoaderStart: PropTypes.func,\n      onNetworksChange: PropTypes.func,\n      onConfirm: PropTypes.func,\n      onCancel: PropTypes.func,\n      onShippingAddressChange: PropTypes.func,\n      onShippingRateChange: PropTypes.func,\n      options: PropTypes.object\n    };\n    Element.displayName = displayName;\n    Element.__elementType = type;\n    return Element;\n  };\n\n  var isServer = typeof window === 'undefined';\n\n  var EmbeddedCheckoutContext = /*#__PURE__*/React.createContext(null);\n  EmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\n  var useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n    var ctx = React.useContext(EmbeddedCheckoutContext);\n\n    if (!ctx) {\n      throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n    }\n\n    return ctx;\n  };\n  var INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n    }, [rawStripeProp]);\n    var embeddedCheckoutPromise = React.useRef(null);\n    var loadedStripe = React.useRef(null);\n\n    var _React$useState = React.useState({\n      embeddedCheckout: null\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      // Don't support any ctx updates once embeddedCheckout or stripe is set.\n      if (loadedStripe.current || embeddedCheckoutPromise.current) {\n        return;\n      }\n\n      var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n        if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n        loadedStripe.current = stripe;\n        embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n          setContext({\n            embeddedCheckout: embeddedCheckout\n          });\n        });\n      }; // For an async stripePromise, store it once resolved\n\n\n      if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe) {\n            setStripeAndInitEmbeddedCheckout(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        // Or, handle a sync stripe instance going from null -> populated\n        setStripeAndInitEmbeddedCheckout(parsed.stripe);\n      }\n    }, [parsed, options, ctx, loadedStripe]);\n    React.useEffect(function () {\n      // cleanup on unmount\n      return function () {\n        // If embedded checkout is fully initialized, destroy it.\n        if (ctx.embeddedCheckout) {\n          embeddedCheckoutPromise.current = null;\n          ctx.embeddedCheckout.destroy();\n        } else if (embeddedCheckoutPromise.current) {\n          // If embedded checkout is still initializing, destroy it once\n          // it's done. This could be caused by unmounting very quickly\n          // after mounting.\n          embeddedCheckoutPromise.current.then(function () {\n            embeddedCheckoutPromise.current = null;\n\n            if (ctx.embeddedCheckout) {\n              ctx.embeddedCheckout.destroy();\n            }\n          });\n        }\n      };\n    }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(loadedStripe);\n    }, [loadedStripe]); // Warn on changes to stripe prop.\n    // The stripe prop value can only go from null to non-null once and\n    // can't be changed after that.\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (prevOptions == null) {\n        return;\n      }\n\n      if (options == null) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n        return;\n      }\n\n      if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n        console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n      }\n\n      if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n      }\n\n      if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n      }\n\n      if (prevOptions.onLineItemsChange != null && options.onLineItemsChange !== prevOptions.onLineItemsChange) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.');\n      }\n    }, [prevOptions, options]);\n    return /*#__PURE__*/React.createElement(EmbeddedCheckoutContext.Provider, {\n      value: ctx\n    }, children);\n  };\n\n  var EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className;\n\n    var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n        embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n    var isMounted = React.useRef(false);\n    var domNode = React.useRef(null);\n    React.useLayoutEffect(function () {\n      if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n        embeddedCheckout.mount(domNode.current);\n        isMounted.current = true;\n      } // Clean up on unmount\n\n\n      return function () {\n        if (isMounted.current && embeddedCheckout) {\n          try {\n            embeddedCheckout.unmount();\n            isMounted.current = false;\n          } catch (e) {// Do nothing.\n            // Parent effects are destroyed before child effects, so\n            // in cases where both the EmbeddedCheckoutProvider and\n            // the EmbeddedCheckout component are removed at the same\n            // time, the embeddedCheckout instance will be destroyed,\n            // which causes an error when calling unmount.\n          }\n        }\n      };\n    }, [embeddedCheckout]);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: domNode,\n      id: id,\n      className: className\n    });\n  }; // Only render the wrapper in a server environment.\n\n\n  var EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n    var id = _ref2.id,\n        className = _ref2.className;\n    // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n    useEmbeddedCheckoutContext();\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n   */\n\n  var useStripe = function useStripe() {\n    var _useElementsOrCheckou = useElementsOrCheckoutSdkContextWithUseCase('calls useStripe()'),\n        stripe = _useElementsOrCheckou.stripe;\n\n    return stripe;\n  };\n\n  /**\n   * Requires beta access:\n   * Contact [Stripe support](https://support.stripe.com/) for more information.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardElement = createElementComponent('card', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardNumberElement = createElementComponent('cardNumber', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardExpiryElement = createElementComponent('cardExpiry', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardCvcElement = createElementComponent('cardCvc', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var FpxBankElement = createElementComponent('fpxBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IbanElement = createElementComponent('iban', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IdealBankElement = createElementComponent('idealBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var P24BankElement = createElementComponent('p24Bank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var EpsBankElement = createElementComponent('epsBank', isServer);\n  var PaymentElement = createElementComponent('payment', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n  /**\n   * Requires beta access:\n   * Contact [Stripe support](https://support.stripe.com/) for more information.\n   */\n\n  var CurrencySelectorElement = createElementComponent('currencySelector', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AddressElement = createElementComponent('address', isServer);\n  /**\n   * @deprecated\n   * Use `AddressElement` instead.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\n  exports.AddressElement = AddressElement;\n  exports.AffirmMessageElement = AffirmMessageElement;\n  exports.AfterpayClearpayMessageElement = AfterpayClearpayMessageElement;\n  exports.AuBankAccountElement = AuBankAccountElement;\n  exports.CardCvcElement = CardCvcElement;\n  exports.CardElement = CardElement;\n  exports.CardExpiryElement = CardExpiryElement;\n  exports.CardNumberElement = CardNumberElement;\n  exports.CheckoutProvider = CheckoutProvider;\n  exports.CurrencySelectorElement = CurrencySelectorElement;\n  exports.Elements = Elements;\n  exports.ElementsConsumer = ElementsConsumer;\n  exports.EmbeddedCheckout = EmbeddedCheckout;\n  exports.EmbeddedCheckoutProvider = EmbeddedCheckoutProvider;\n  exports.EpsBankElement = EpsBankElement;\n  exports.ExpressCheckoutElement = ExpressCheckoutElement;\n  exports.FpxBankElement = FpxBankElement;\n  exports.IbanElement = IbanElement;\n  exports.IdealBankElement = IdealBankElement;\n  exports.LinkAuthenticationElement = LinkAuthenticationElement;\n  exports.P24BankElement = P24BankElement;\n  exports.PaymentElement = PaymentElement;\n  exports.PaymentMethodMessagingElement = PaymentMethodMessagingElement;\n  exports.PaymentRequestButtonElement = PaymentRequestButtonElement;\n  exports.ShippingAddressElement = ShippingAddressElement;\n  exports.useCheckout = useCheckout;\n  exports.useElements = useElements;\n  exports.useStripe = useStripe;\n\n}));\n"], "names": [], "mappings": "AAAA,CAAC,SAAU,MAAM,EAAE,OAAO;IACxB,uCAA+D,QAAQ;AAGzE,CAAC,EAAE,IAAI,EAAG,SAAU,QAAO,EAAE,KAAK;IAAI;IAEpC,SAAS,QAAQ,MAAM,EAAE,cAAc;QACrC,IAAI,OAAO,OAAO,IAAI,CAAC;QAEvB,IAAI,OAAO,qBAAqB,EAAE;YAChC,IAAI,UAAU,OAAO,qBAAqB,CAAC;YAE3C,IAAI,gBAAgB;gBAClB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG;oBACpC,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;gBAChE;YACF;YAEA,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACxB;QAEA,OAAO;IACT;IAEA,SAAS,eAAe,MAAM;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;YAEpD,IAAI,IAAI,GAAG;gBACT,QAAQ,OAAO,SAAS,MAAM,OAAO,CAAC,SAAU,GAAG;oBACjD,gBAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;gBAC1C;YACF,OAAO,IAAI,OAAO,yBAAyB,EAAE;gBAC3C,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;YACnE,OAAO;gBACL,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG;oBAC3C,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;gBAC7E;YACF;QACF;QAEA,OAAO;IACT;IAEA,SAAS,QAAQ,GAAG;QAClB;QAEA,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,QAAQ,KAAK,UAAU;YACvE,UAAU,SAAU,GAAG;gBACrB,OAAO,OAAO;YAChB;QACF,OAAO;YACL,UAAU,SAAU,GAAG;gBACrB,OAAO,OAAO,OAAO,WAAW,cAAc,IAAI,WAAW,KAAK,UAAU,QAAQ,OAAO,SAAS,GAAG,WAAW,OAAO;YAC3H;QACF;QAEA,OAAO,QAAQ;IACjB;IAEA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;QACtC,IAAI,OAAO,KAAK;YACd,OAAO,cAAc,CAAC,KAAK,KAAK;gBAC9B,OAAO;gBACP,YAAY;gBACZ,cAAc;gBACd,UAAU;YACZ;QACF,OAAO;YACL,GAAG,CAAC,IAAI,GAAG;QACb;QAEA,OAAO;IACT;IAEA,SAAS,8BAA8B,MAAM,EAAE,QAAQ;QACrD,IAAI,UAAU,MAAM,OAAO,CAAC;QAC5B,IAAI,SAAS,CAAC;QACd,IAAI,aAAa,OAAO,IAAI,CAAC;QAC7B,IAAI,KAAK;QAET,IAAK,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACtC,MAAM,UAAU,CAAC,EAAE;YACnB,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;YAChC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;QAEA,OAAO;IACT;IAEA,SAAS,yBAAyB,MAAM,EAAE,QAAQ;QAChD,IAAI,UAAU,MAAM,OAAO,CAAC;QAE5B,IAAI,SAAS,8BAA8B,QAAQ;QAEnD,IAAI,KAAK;QAET,IAAI,OAAO,qBAAqB,EAAE;YAChC,IAAI,mBAAmB,OAAO,qBAAqB,CAAC;YAEpD,IAAK,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAC5C,MAAM,gBAAgB,CAAC,EAAE;gBACzB,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;gBAChC,IAAI,CAAC,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,MAAM;gBAC9D,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;YAC3B;QACF;QAEA,OAAO;IACT;IAEA,SAAS,eAAe,GAAG,EAAE,CAAC;QAC5B,OAAO,gBAAgB,QAAQ,sBAAsB,KAAK,MAAM,4BAA4B,KAAK,MAAM;IACzG;IAEA,SAAS,gBAAgB,GAAG;QAC1B,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO;IACjC;IAEA,SAAS,sBAAsB,GAAG,EAAE,CAAC;QACnC,IAAI,KAAK,OAAO,CAAC,OAAO,WAAW,eAAe,GAAG,CAAC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,aAAa;QAE3F,IAAI,MAAM,MAAM;QAChB,IAAI,OAAO,EAAE;QACb,IAAI,KAAK;QACT,IAAI,KAAK;QAET,IAAI,IAAI;QAER,IAAI;YACF,IAAK,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,EAAE,EAAE,IAAI,GAAG,KAAK,KAAM;gBAChE,KAAK,IAAI,CAAC,GAAG,KAAK;gBAElB,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,KAAK;YACL,KAAK;QACP,SAAU;YACR,IAAI;gBACF,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC,SAAS;YAC/C,SAAU;gBACR,IAAI,IAAI,MAAM;YAChB;QACF;QAEA,OAAO;IACT;IAEA,SAAS,4BAA4B,CAAC,EAAE,MAAM;QAC5C,IAAI,CAAC,GAAG;QACR,IAAI,OAAO,MAAM,UAAU,OAAO,kBAAkB,GAAG;QACvD,IAAI,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;QACpD,IAAI,MAAM,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;QAC3D,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;QAClD,IAAI,MAAM,eAAe,2CAA2C,IAAI,CAAC,IAAI,OAAO,kBAAkB,GAAG;IAC3G;IAEA,SAAS,kBAAkB,GAAG,EAAE,GAAG;QACjC,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,MAAM;QAErD,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QAErE,OAAO;IACT;IAEA,SAAS;QACP,MAAM,IAAI,UAAU;IACtB;IAEA,SAAS,wBAAyB,CAAC;QAClC,OAAO,KAAK,EAAE,UAAU,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC,UAAU,GAAG;IACjG;IAEA,IAAI,YAAY;QAAC,SAAS,CAAC;IAAC;IAE5B;;;;;GAKC,GACD,IAAI;IACJ,IAAI;IAEJ,SAAS;QACP,IAAI,iCAAiC,OAAO;QAC5C,kCAAkC;QAElC,IAAI,uBAAuB;QAC3B,yBAAyB;QACzB,OAAO;IACT;IAEA;;;;;GAKC,GACD,IAAI;IACJ,IAAI;IAEJ,SAAS;QACP,IAAI,qCAAqC,OAAO;QAChD,sCAAsC;QAEtC,IAAI,uBAAuB;QAE3B,SAAS,iBAAiB;QAE1B,SAAS,0BAA0B;QAEnC,uBAAuB,iBAAiB,GAAG;QAE3C,2BAA2B;YACzB,SAAS,KAAK,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM;gBAC1E,IAAI,WAAW,sBAAsB;oBACnC,2CAA2C;oBAC3C;gBACF;gBAEA,IAAI,MAAM,IAAI,MAAM,yFAAyF,kDAAkD;gBAC/J,IAAI,IAAI,GAAG;gBACX,MAAM;YACR;YACA,KAAK,UAAU,GAAG;YAElB,SAAS;gBACP,OAAO;YACT;YACA,oFAAoF;YAEpF,IAAI,iBAAiB;gBACnB,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;gBACb,YAAY;gBACZ,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,mBAAmB;YACrB;YACA,eAAe,SAAS,GAAG;YAC3B,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;;;;GAKC,GAED;QACE,gFAAgF;QAChF,kCAAkC;QAClC,UAAU,OAAO,GAAG;IACtB;IAEA,IAAI,mBAAmB,UAAU,OAAO;IACxC,IAAI,YAAY,WAAW,GAAE,wBAAwB;IAErD,IAAI,iBAAiB,SAAS,eAAe,OAAO,EAAE,KAAK,EAAE,EAAE;QAC7D,IAAI,YAAY,CAAC,CAAC;QAClB,IAAI,QAAQ,MAAM,MAAM,CAAC,KAAK,iEAAiE;QAC/F,kEAAkE;QAElE,MAAM,SAAS;wCAAC;gBACd,MAAM,OAAO,GAAG;YAClB;uCAAG;YAAC;SAAG;QACP,MAAM,SAAS;wCAAC;gBACd,IAAI,CAAC,aAAa,CAAC,SAAS;oBAC1B;oDAAO,YAAa;;gBACtB;gBAEA,IAAI,cAAc,SAAS;oBACzB,IAAI,MAAM,OAAO,EAAE;wBACjB,MAAM,OAAO,CAAC,KAAK,CAAC,OAAO;oBAC7B;gBACF;gBAEA,QAAQ,EAAE,CAAC,OAAO;gBAClB;gDAAO;wBACL,QAAQ,GAAG,CAAC,OAAO;oBACrB;;YACF;uCAAG;YAAC;YAAW;YAAO;YAAS;SAAM;IACvC;IAEA,IAAI,cAAc,SAAS,YAAY,KAAK;QAC1C,IAAI,MAAM,MAAM,MAAM,CAAC;QACvB,MAAM,SAAS;qCAAC;gBACd,IAAI,OAAO,GAAG;YAChB;oCAAG;YAAC;SAAM;QACV,OAAO,IAAI,OAAO;IACpB;IAEA,IAAI,kBAAkB,SAAS,gBAAgB,GAAG;QAChD,OAAO,QAAQ,QAAQ,QAAQ,SAAS;IAC1C;IACA,IAAI,YAAY,SAAS,UAAU,GAAG;QACpC,OAAO,gBAAgB,QAAQ,OAAO,IAAI,IAAI,KAAK;IACrD,GAAG,+DAA+D;IAClE,uEAAuE;IACvE,uDAAuD;IAEvD,IAAI,WAAW,SAAS,SAAS,GAAG;QAClC,OAAO,gBAAgB,QAAQ,OAAO,IAAI,QAAQ,KAAK,cAAc,OAAO,IAAI,WAAW,KAAK,cAAc,OAAO,IAAI,mBAAmB,KAAK,cAAc,OAAO,IAAI,kBAAkB,KAAK;IACnM;IAEA,IAAI,mBAAmB;IACvB,IAAI,UAAU,SAAS,QAAQ,IAAI,EAAE,KAAK;QACxC,IAAI,CAAC,gBAAgB,SAAS,CAAC,gBAAgB,QAAQ;YACrD,OAAO,SAAS;QAClB;QAEA,IAAI,YAAY,MAAM,OAAO,CAAC;QAC9B,IAAI,aAAa,MAAM,OAAO,CAAC;QAC/B,IAAI,cAAc,YAAY,OAAO;QACrC,IAAI,kBAAkB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;QAC/D,IAAI,mBAAmB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;QACjE,IAAI,oBAAoB,kBAAkB,OAAO,OAAO,0EAA0E;QAClI,+BAA+B;QAE/B,IAAI,CAAC,mBAAmB,CAAC,WAAW,OAAO,SAAS;QACpD,IAAI,WAAW,OAAO,IAAI,CAAC;QAC3B,IAAI,YAAY,OAAO,IAAI,CAAC;QAC5B,IAAI,SAAS,MAAM,KAAK,UAAU,MAAM,EAAE,OAAO;QACjD,IAAI,SAAS,CAAC;QAEd,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;YAC3C,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG;QACxB;QAEA,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,MAAM,EAAG;YAC/C,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG;QAC1B;QAEA,IAAI,UAAU,OAAO,IAAI,CAAC;QAE1B,IAAI,QAAQ,MAAM,KAAK,SAAS,MAAM,EAAE;YACtC,OAAO;QACT;QAEA,IAAI,IAAI;QACR,IAAI,IAAI;QAER,IAAI,OAAO,SAAS,KAAK,GAAG;YAC1B,OAAO,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;QAC/B;QAEA,OAAO,QAAQ,KAAK,CAAC;IACvB;IAEA,IAAI,+BAA+B,SAAS,6BAA6B,OAAO,EAAE,WAAW,EAAE,aAAa;QAC1G,IAAI,CAAC,gBAAgB,UAAU;YAC7B,OAAO;QACT;QAEA,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,UAAU,EAAE,GAAG;YAC1D,IAAI,YAAY,CAAC,gBAAgB,gBAAgB,CAAC,QAAQ,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI;YAExF,IAAI,cAAc,QAAQ,CAAC,MAAM;gBAC/B,IAAI,WAAW;oBACb,QAAQ,IAAI,CAAC,oCAAoC,MAAM,CAAC,KAAK;gBAC/D;gBAEA,OAAO;YACT;YAEA,IAAI,CAAC,WAAW;gBACd,OAAO;YACT;YAEA,OAAO,eAAe,eAAe,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,GAAG,KAAK,OAAO,CAAC,IAAI;QACvG,GAAG;IACL;IAEA,IAAI,yBAAyB,sMAAsM,6EAA6E;IAChT,uEAAuE;IACvE,qCAAqC;IAErC,IAAI,iBAAiB,SAAS,eAAe,WAAW;QACtD,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAEnF,IAAI,gBAAgB,QAAQ,SAAS,cAAc;YACjD,OAAO;QACT;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,kBAAkB,SAAS,gBAAgB,GAAG;QAChD,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAEnF,IAAI,UAAU,MAAM;YAClB,OAAO;gBACL,KAAK;gBACL,eAAe,QAAQ,OAAO,CAAC,KAAK,IAAI,CAAC,SAAU,MAAM;oBACvD,OAAO,eAAe,QAAQ;gBAChC;YACF;QACF;QAEA,IAAI,SAAS,eAAe,KAAK;QAEjC,IAAI,WAAW,MAAM;YACnB,OAAO;gBACL,KAAK;YACP;QACF;QAEA,OAAO;YACL,KAAK;YACL,QAAQ;QACV;IACF;IAEA,IAAI,uBAAuB,SAAS,qBAAqB,MAAM;QAC7D,IAAI,CAAC,UAAU,CAAC,OAAO,gBAAgB,IAAI,CAAC,OAAO,eAAe,EAAE;YAClE;QACF;QAEA,OAAO,gBAAgB,CAAC;YACtB,MAAM;YACN,SAAS;QACX;QAEA,OAAO,eAAe,CAAC;YACrB,MAAM;YACN,SAAS;YACT,KAAK;QACP;IACF;IAEA,IAAI,kBAAkB,WAAW,GAAE,MAAM,aAAa,CAAC;IACvD,gBAAgB,WAAW,GAAG;IAC9B,IAAI,uBAAuB,SAAS,qBAAqB,GAAG,EAAE,OAAO;QACnE,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM,+EAA+E,MAAM,CAAC,SAAS;QACjH;QAEA,OAAO;IACT;IACA;;;;;;;;;GASC,GAED,IAAI,WAAW,SAAS,SAAS,IAAI;QACnC,IAAI,gBAAgB,KAAK,MAAM,EAC3B,UAAU,KAAK,OAAO,EACtB,WAAW,KAAK,QAAQ;QAC5B,IAAI,SAAS,MAAM,OAAO;wCAAC;gBACzB,OAAO,gBAAgB;YACzB;uCAAG;YAAC;SAAc,GAAG,sDAAsD;QAE3E,IAAI,kBAAkB,MAAM,QAAQ;kDAAC;gBACnC,OAAO;oBACL,QAAQ,OAAO,GAAG,KAAK,SAAS,OAAO,MAAM,GAAG;oBAChD,UAAU,OAAO,GAAG,KAAK,SAAS,OAAO,MAAM,CAAC,QAAQ,CAAC,WAAW;gBACtE;YACF;kDACI,mBAAmB,eAAe,iBAAiB,IACnD,MAAM,gBAAgB,CAAC,EAAE,EACzB,aAAa,gBAAgB,CAAC,EAAE;QAEpC,MAAM,SAAS;kCAAC;gBACd,IAAI,YAAY;gBAEhB,IAAI,iBAAiB,SAAS,eAAe,MAAM;oBACjD;6DAAW,SAAU,GAAG;4BACtB,oGAAoG;4BACpG,IAAI,IAAI,MAAM,EAAE,OAAO;4BACvB,OAAO;gCACL,QAAQ;gCACR,UAAU,OAAO,QAAQ,CAAC;4BAC5B;wBACF;;gBACF,GAAG,gEAAgE;gBAGnE,IAAI,OAAO,GAAG,KAAK,WAAW,CAAC,IAAI,MAAM,EAAE;oBACzC,OAAO,aAAa,CAAC,IAAI;8CAAC,SAAU,MAAM;4BACxC,IAAI,UAAU,WAAW;gCACvB,iEAAiE;gCACjE,6DAA6D;gCAC7D,uBAAuB;gCACvB,eAAe;4BACjB;wBACF;;gBACF,OAAO,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,IAAI,MAAM,EAAE;oBAC/C,iEAAiE;oBACjE,eAAe,OAAO,MAAM;gBAC9B;gBAEA;0CAAO;wBACL,YAAY;oBACd;;YACF;iCAAG;YAAC;YAAQ;YAAK;SAAQ,GAAG,iCAAiC;QAE7D,IAAI,aAAa,YAAY;QAC7B,MAAM,SAAS;kCAAC;gBACd,IAAI,eAAe,QAAQ,eAAe,eAAe;oBACvD,QAAQ,IAAI,CAAC;gBACf;YACF;iCAAG;YAAC;YAAY;SAAc,GAAG,mEAAmE;QAEpG,IAAI,cAAc,YAAY;QAC9B,MAAM,SAAS;kCAAC;gBACd,IAAI,CAAC,IAAI,QAAQ,EAAE;oBACjB;gBACF;gBAEA,IAAI,UAAU,6BAA6B,SAAS,aAAa;oBAAC;oBAAgB;iBAAQ;gBAE1F,IAAI,SAAS;oBACX,IAAI,QAAQ,CAAC,MAAM,CAAC;gBACtB;YACF;iCAAG;YAAC;YAAS;YAAa,IAAI,QAAQ;SAAC,GAAG,uDAAuD;QAEjG,MAAM,SAAS;kCAAC;gBACd,qBAAqB,IAAI,MAAM;YACjC;iCAAG;YAAC,IAAI,MAAM;SAAC;QACf,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,gBAAgB,QAAQ,EAAE;YAChE,OAAO;QACT,GAAG;IACL;IACA,SAAS,SAAS,GAAG;QACnB,QAAQ,UAAU,GAAG;QACrB,SAAS,UAAU,MAAM;IAC3B;IACA,IAAI,gCAAgC,SAAS,8BAA8B,cAAc;QACvF,IAAI,MAAM,MAAM,UAAU,CAAC;QAC3B,OAAO,qBAAqB,KAAK;IACnC;IACA;;GAEC,GAED,IAAI,cAAc,SAAS;QACzB,IAAI,wBAAwB,8BAA8B,wBACtD,WAAW,sBAAsB,QAAQ;QAE7C,OAAO;IACT;IACA;;GAEC,GAED,IAAI,mBAAmB,SAAS,iBAAiB,KAAK;QACpD,IAAI,WAAW,MAAM,QAAQ;QAC7B,IAAI,MAAM,8BAA8B,8BAA8B,6EAA6E;QAEnJ,OAAO,SAAS;IAClB;IACA,iBAAiB,SAAS,GAAG;QAC3B,UAAU,UAAU,IAAI,CAAC,UAAU;IACrC;IAEA,IAAI,cAAc;QAAC;QAAM;KAAU;IACnC,IAAI,qBAAqB,WAAW,GAAE,MAAM,aAAa,CAAC;IAC1D,mBAAmB,WAAW,GAAG;IACjC,IAAI,0BAA0B,SAAS,wBAAwB,GAAG,EAAE,OAAO;QACzE,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM,uFAAuF,MAAM,CAAC,SAAS;QACzH;QAEA,OAAO;IACT;IACA,IAAI,kBAAkB,WAAW,GAAE,MAAM,aAAa,CAAC;IACvD,gBAAgB,WAAW,GAAG;IAC9B,IAAI,8BAA8B,SAAS,4BAA4B,WAAW,EAAE,YAAY;QAC9F,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QAEA,YAAY,EAAE;QACV,YAAY,OAAO;QACnB,IAAI,UAAU,yBAAyB,aAAa;QAExD,IAAI,CAAC,cAAc;YACjB,OAAO,OAAO,MAAM,CAAC,YAAY,OAAO,IAAI;QAC9C;QAEA,OAAO,OAAO,MAAM,CAAC,cAAc;IACrC;IACA,IAAI,yBAAyB;IAC7B,IAAI,mBAAmB,SAAS,iBAAiB,IAAI;QACnD,IAAI,gBAAgB,KAAK,MAAM,EAC3B,UAAU,KAAK,OAAO,EACtB,WAAW,KAAK,QAAQ;QAC5B,IAAI,SAAS,MAAM,OAAO;gDAAC;gBACzB,OAAO,gBAAgB,eAAe;YACxC;+CAAG;YAAC;SAAc,GAAG,gEAAgE;QAErF,IAAI,kBAAkB,MAAM,QAAQ,CAAC,OACjC,mBAAmB,eAAe,iBAAiB,IACnD,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;QAEpC,IAAI,mBAAmB,MAAM,QAAQ;2DAAC;gBACpC,OAAO;oBACL,QAAQ,OAAO,GAAG,KAAK,SAAS,OAAO,MAAM,GAAG;oBAChD,aAAa;gBACf;YACF;2DACI,mBAAmB,eAAe,kBAAkB,IACpD,MAAM,gBAAgB,CAAC,EAAE,EACzB,aAAa,gBAAgB,CAAC,EAAE;QAEpC,IAAI,iBAAiB,SAAS,eAAe,MAAM,EAAE,WAAW;YAC9D,WAAW,SAAU,GAAG;gBACtB,IAAI,IAAI,MAAM,IAAI,IAAI,WAAW,EAAE;oBACjC,OAAO;gBACT;gBAEA,OAAO;oBACL,QAAQ;oBACR,aAAa;gBACf;YACF;QACF,GAAG,6EAA6E;QAGhF,IAAI,wBAAwB,MAAM,MAAM,CAAC;QACzC,MAAM,SAAS;0CAAC;gBACd,IAAI,YAAY;gBAEhB,IAAI,OAAO,GAAG,KAAK,WAAW,CAAC,IAAI,MAAM,EAAE;oBACzC,OAAO,aAAa,CAAC,IAAI;sDAAC,SAAU,MAAM;4BACxC,IAAI,UAAU,aAAa,CAAC,sBAAsB,OAAO,EAAE;gCACzD,wDAAwD;gCACxD,6DAA6D;gCAC7D,uBAAuB;gCACvB,sBAAsB,OAAO,GAAG;gCAChC,OAAO,YAAY,CAAC,SAAS,IAAI;kEAAC,SAAU,WAAW;wCACrD,IAAI,aAAa;4CACf,eAAe,QAAQ;4CACvB,YAAY,EAAE,CAAC,UAAU;wCAC3B;oCACF;;4BACF;wBACF;;gBACF,OAAO,IAAI,OAAO,GAAG,KAAK,UAAU,OAAO,MAAM,IAAI,CAAC,sBAAsB,OAAO,EAAE;oBACnF,sBAAsB,OAAO,GAAG;oBAChC,OAAO,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI;sDAAC,SAAU,WAAW;4BAC5D,IAAI,aAAa;gCACf,eAAe,OAAO,MAAM,EAAE;gCAC9B,YAAY,EAAE,CAAC,UAAU;4BAC3B;wBACF;;gBACF;gBAEA;kDAAO;wBACL,YAAY;oBACd;;YACF;yCAAG;YAAC;YAAQ;YAAK;YAAS;SAAW,GAAG,iCAAiC;QAEzE,IAAI,aAAa,YAAY;QAC7B,MAAM,SAAS;0CAAC;gBACd,IAAI,eAAe,QAAQ,eAAe,eAAe;oBACvD,QAAQ,IAAI,CAAC;gBACf;YACF;yCAAG;YAAC;YAAY;SAAc,GAAG,mEAAmE;QAEpG,IAAI,cAAc,YAAY;QAC9B,IAAI,kBAAkB,YAAY,IAAI,WAAW;QACjD,MAAM,SAAS;0CAAC;gBACd,IAAI,uBAAuB;gBAE3B,wDAAwD;gBACxD,IAAI,CAAC,IAAI,WAAW,EAAE;oBACpB;gBACF;gBAEA,IAAI,qBAAqB,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,CAAC,wBAAwB,YAAY,eAAe,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,UAAU;gBACjO,IAAI,oBAAoB,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,CAAC,wBAAwB,QAAQ,eAAe,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,UAAU;gBACpN,IAAI,uBAAuB,CAAC,QAAQ,mBAAmB;gBACvD,IAAI,eAAe,CAAC,mBAAmB,IAAI,WAAW;gBAEtD,IAAI,qBAAqB,CAAC,wBAAwB,YAAY,GAAG;oBAC/D,IAAI,WAAW,CAAC,gBAAgB,CAAC;gBACnC;YACF;yCAAG;YAAC;YAAS;YAAa,IAAI,WAAW;YAAE;SAAgB,GAAG,uDAAuD;QAErH,MAAM,SAAS;0CAAC;gBACd,qBAAqB,IAAI,MAAM;YACjC;yCAAG;YAAC,IAAI,MAAM;SAAC;QACf,IAAI,uBAAuB,MAAM,OAAO;8DAAC;gBACvC,OAAO,4BAA4B,IAAI,WAAW,EAAE;YACtD;6DAAG;YAAC,IAAI,WAAW;YAAE;SAAQ;QAE7B,IAAI,CAAC,IAAI,WAAW,EAAE;YACpB,OAAO;QACT;QAEA,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,mBAAmB,QAAQ,EAAE;YACnE,OAAO;QACT,GAAG,WAAW,GAAE,MAAM,aAAa,CAAC,gBAAgB,QAAQ,EAAE;YAC5D,OAAO;QACT,GAAG;IACL;IACA,iBAAiB,SAAS,GAAG;QAC3B,QAAQ,UAAU,GAAG;QACrB,SAAS,UAAU,KAAK,CAAC;YACvB,mBAAmB,UAAU,IAAI,CAAC,UAAU;YAC5C,iBAAiB,UAAU,MAAM;QACnC,GAAG,UAAU;IACf;IACA,IAAI,mCAAmC,SAAS,iCAAiC,aAAa;QAC5F,IAAI,MAAM,MAAM,UAAU,CAAC;QAC3B,OAAO,wBAAwB,KAAK;IACtC;IACA,IAAI,6CAA6C,SAAS,2CAA2C,aAAa;QAChH,IAAI,qBAAqB,MAAM,UAAU,CAAC;QAC1C,IAAI,kBAAkB,MAAM,UAAU,CAAC;QAEvC,IAAI,sBAAsB,iBAAiB;YACzC,MAAM,IAAI,MAAM,6CAA6C,MAAM,CAAC,eAAe;QACrF;QAEA,IAAI,oBAAoB;YACtB,OAAO,wBAAwB,oBAAoB;QACrD;QAEA,OAAO,qBAAqB,iBAAiB;IAC/C;IACA,IAAI,cAAc,SAAS;QACzB,kCAAkC;QAClC,iCAAiC;QACjC,IAAI,MAAM,MAAM,UAAU,CAAC;QAE3B,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IAEA,IAAI,YAAY;QAAC;KAAO;IAExB,IAAI,cAAc,SAAS,YAAY,GAAG;QACxC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;IACjD;IAEA,IAAI,yBAAyB,SAAS,uBAAuB,IAAI,EAAE,QAAQ;QACzE,IAAI,cAAc,GAAG,MAAM,CAAC,YAAY,OAAO;QAE/C,IAAI,gBAAgB,SAAS,cAAc,IAAI;YAC7C,IAAI,KAAK,KAAK,EAAE,EACZ,YAAY,KAAK,SAAS,EAC1B,eAAe,KAAK,OAAO,EAC3B,UAAU,iBAAiB,KAAK,IAAI,CAAC,IAAI,cACzC,SAAS,KAAK,MAAM,EACpB,UAAU,KAAK,OAAO,EACtB,UAAU,KAAK,OAAO,EACtB,WAAW,KAAK,QAAQ,EACxB,WAAW,KAAK,QAAQ,EACxB,UAAU,KAAK,OAAO,EACtB,cAAc,KAAK,WAAW,EAC9B,gBAAgB,KAAK,aAAa,EAClC,mBAAmB,KAAK,gBAAgB,EACxC,YAAY,KAAK,SAAS,EAC1B,WAAW,KAAK,QAAQ,EACxB,0BAA0B,KAAK,uBAAuB,EACtD,uBAAuB,KAAK,oBAAoB;YACpD,IAAI,MAAM,2CAA2C,WAAW,MAAM,CAAC,aAAa;YACpF,IAAI,WAAW,cAAc,MAAM,IAAI,QAAQ,GAAG;YAClD,IAAI,cAAc,iBAAiB,MAAM,IAAI,WAAW,GAAG;YAE3D,IAAI,kBAAkB,MAAM,QAAQ,CAAC,OACjC,mBAAmB,eAAe,iBAAiB,IACnD,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;YAEpC,IAAI,aAAa,MAAM,MAAM,CAAC;YAC9B,IAAI,UAAU,MAAM,MAAM,CAAC,OAAO,0EAA0E;YAC5G,yEAAyE;YACzE,+EAA+E;YAE/E,eAAe,SAAS,QAAQ;YAChC,eAAe,SAAS,SAAS;YACjC,eAAe,SAAS,UAAU;YAClC,eAAe,SAAS,SAAS;YACjC,eAAe,SAAS,aAAa;YACrC,eAAe,SAAS,eAAe;YACvC,eAAe,SAAS,kBAAkB;YAC1C,eAAe,SAAS,WAAW;YACnC,eAAe,SAAS,UAAU;YAClC,eAAe,SAAS,yBAAyB;YACjD,eAAe,SAAS,sBAAsB;YAC9C,eAAe,SAAS,UAAU;YAClC,IAAI;YAEJ,IAAI,SAAS;gBACX,IAAI,SAAS,mBAAmB;oBAC9B,4DAA4D;oBAC5D,gBAAgB;gBAClB,OAAO;oBACL,uDAAuD;oBACvD,gBAAgB,SAAS;wBACvB,QAAQ;oBACV;gBACF;YACF;YAEA,eAAe,SAAS,SAAS;YACjC,MAAM,eAAe;wEAAC;oBACpB,IAAI,WAAW,OAAO,KAAK,QAAQ,QAAQ,OAAO,KAAK,QAAQ,CAAC,YAAY,WAAW,GAAG;wBACxF,IAAI,aAAa;wBAEjB,IAAI,aAAa;4BACf,OAAQ;gCACN,KAAK;oCACH,aAAa,YAAY,oBAAoB,CAAC;oCAC9C;gCAEF,KAAK;oCACH,IAAI,UAAU,SAAS;wCACrB,IAAI,OAAO,QAAQ,IAAI,EACnB,cAAc,yBAAyB,SAAS;wCAEpD,IAAI,SAAS,YAAY;4CACvB,aAAa,YAAY,4BAA4B,CAAC;wCACxD,OAAO,IAAI,SAAS,WAAW;4CAC7B,aAAa,YAAY,2BAA2B,CAAC;wCACvD,OAAO;4CACL,MAAM,IAAI,MAAM;wCAClB;oCACF,OAAO;wCACL,MAAM,IAAI,MAAM;oCAClB;oCAEA;gCAEF,KAAK;oCACH,aAAa,YAAY,4BAA4B,CAAC;oCACtD;gCAEF,KAAK;oCACH,aAAa,YAAY,6BAA6B;oCACtD;gCAEF;oCACE,MAAM,IAAI,MAAM,wBAAwB,MAAM,CAAC,aAAa;4BAChE;wBACF,OAAO,IAAI,UAAU;4BACnB,aAAa,SAAS,MAAM,CAAC,MAAM;wBACrC,EAAE,+FAA+F;wBAGjG,WAAW,OAAO,GAAG,YAAY,iEAAiE;wBAElG,WAAW;wBAEX,IAAI,YAAY;4BACd,WAAW,KAAK,CAAC,QAAQ,OAAO;wBAClC;oBACF;gBACF;uEAAG;gBAAC;gBAAU;gBAAa;aAAQ;YACnC,IAAI,cAAc,YAAY;YAC9B,MAAM,SAAS;kEAAC;oBACd,IAAI,CAAC,WAAW,OAAO,EAAE;wBACvB;oBACF;oBAEA,IAAI,UAAU,6BAA6B,SAAS,aAAa;wBAAC;qBAAiB;oBAEnF,IAAI,WAAW,YAAY,WAAW,OAAO,EAAE;wBAC7C,WAAW,OAAO,CAAC,MAAM,CAAC;oBAC5B;gBACF;iEAAG;gBAAC;gBAAS;aAAY;YACzB,MAAM,eAAe;wEAAC;oBACpB;gFAAO;4BACL,IAAI,WAAW,OAAO,IAAI,OAAO,WAAW,OAAO,CAAC,OAAO,KAAK,YAAY;gCAC1E,IAAI;oCACF,WAAW,OAAO,CAAC,OAAO;oCAC1B,WAAW,OAAO,GAAG;gCACvB,EAAE,OAAO,OAAO,CAChB;4BACF;wBACF;;gBACF;uEAAG,EAAE;YACL,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;gBAC7C,IAAI;gBACJ,WAAW;gBACX,KAAK;YACP;QACF,GAAG,2DAA2D;QAG9D,IAAI,gBAAgB,SAAS,cAAc,KAAK;YAC9C,2CAA2C,WAAW,MAAM,CAAC,aAAa;YAC1E,IAAI,KAAK,MAAM,EAAE,EACb,YAAY,MAAM,SAAS;YAC/B,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;gBAC7C,IAAI;gBACJ,WAAW;YACb;QACF;QAEA,IAAI,UAAU,WAAW,gBAAgB;QACzC,QAAQ,SAAS,GAAG;YAClB,IAAI,UAAU,MAAM;YACpB,WAAW,UAAU,MAAM;YAC3B,UAAU,UAAU,IAAI;YACxB,QAAQ,UAAU,IAAI;YACtB,SAAS,UAAU,IAAI;YACvB,SAAS,UAAU,IAAI;YACvB,UAAU,UAAU,IAAI;YACxB,SAAS,UAAU,IAAI;YACvB,aAAa,UAAU,IAAI;YAC3B,eAAe,UAAU,IAAI;YAC7B,kBAAkB,UAAU,IAAI;YAChC,WAAW,UAAU,IAAI;YACzB,UAAU,UAAU,IAAI;YACxB,yBAAyB,UAAU,IAAI;YACvC,sBAAsB,UAAU,IAAI;YACpC,SAAS,UAAU,MAAM;QAC3B;QACA,QAAQ,WAAW,GAAG;QACtB,QAAQ,aAAa,GAAG;QACxB,OAAO;IACT;IAEA,IAAI,WAAW,OAAO,WAAW;IAEjC,IAAI,0BAA0B,WAAW,GAAE,MAAM,aAAa,CAAC;IAC/D,wBAAwB,WAAW,GAAG;IACtC,IAAI,6BAA6B,SAAS;QACxC,IAAI,MAAM,MAAM,UAAU,CAAC;QAE3B,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IACA,IAAI,uBAAuB;IAC3B,IAAI,2BAA2B,SAAS,yBAAyB,IAAI;QACnE,IAAI,gBAAgB,KAAK,MAAM,EAC3B,UAAU,KAAK,OAAO,EACtB,WAAW,KAAK,QAAQ;QAC5B,IAAI,SAAS,MAAM,OAAO;wDAAC;gBACzB,OAAO,gBAAgB,eAAe;YACxC;uDAAG;YAAC;SAAc;QAClB,IAAI,0BAA0B,MAAM,MAAM,CAAC;QAC3C,IAAI,eAAe,MAAM,MAAM,CAAC;QAEhC,IAAI,kBAAkB,MAAM,QAAQ,CAAC;YACnC,kBAAkB;QACpB,IACI,mBAAmB,eAAe,iBAAiB,IACnD,MAAM,gBAAgB,CAAC,EAAE,EACzB,aAAa,gBAAgB,CAAC,EAAE;QAEpC,MAAM,SAAS;kDAAC;gBACd,wEAAwE;gBACxE,IAAI,aAAa,OAAO,IAAI,wBAAwB,OAAO,EAAE;oBAC3D;gBACF;gBAEA,IAAI,mCAAmC,SAAS,iCAAiC,MAAM;oBACrF,IAAI,aAAa,OAAO,IAAI,wBAAwB,OAAO,EAAE;oBAC7D,aAAa,OAAO,GAAG;oBACvB,wBAAwB,OAAO,GAAG,aAAa,OAAO,CAAC,oBAAoB,CAAC,SAAS,IAAI;+FAAC,SAAU,gBAAgB;4BAClH,WAAW;gCACT,kBAAkB;4BACpB;wBACF;;gBACF,GAAG,qDAAqD;gBAGxD,IAAI,OAAO,GAAG,KAAK,WAAW,CAAC,aAAa,OAAO,IAAI,CAAC,QAAQ,YAAY,IAAI,QAAQ,iBAAiB,GAAG;oBAC1G,OAAO,aAAa,CAAC,IAAI;8DAAC,SAAU,MAAM;4BACxC,IAAI,QAAQ;gCACV,iCAAiC;4BACnC;wBACF;;gBACF,OAAO,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,aAAa,OAAO,IAAI,CAAC,QAAQ,YAAY,IAAI,QAAQ,iBAAiB,GAAG;oBAChH,iEAAiE;oBACjE,iCAAiC,OAAO,MAAM;gBAChD;YACF;iDAAG;YAAC;YAAQ;YAAS;YAAK;SAAa;QACvC,MAAM,SAAS;kDAAC;gBACd,qBAAqB;gBACrB;0DAAO;wBACL,yDAAyD;wBACzD,IAAI,IAAI,gBAAgB,EAAE;4BACxB,wBAAwB,OAAO,GAAG;4BAClC,IAAI,gBAAgB,CAAC,OAAO;wBAC9B,OAAO,IAAI,wBAAwB,OAAO,EAAE;4BAC1C,8DAA8D;4BAC9D,6DAA6D;4BAC7D,kBAAkB;4BAClB,wBAAwB,OAAO,CAAC,IAAI;sEAAC;oCACnC,wBAAwB,OAAO,GAAG;oCAElC,IAAI,IAAI,gBAAgB,EAAE;wCACxB,IAAI,gBAAgB,CAAC,OAAO;oCAC9B;gCACF;;wBACF;oBACF;;YACF;iDAAG;YAAC,IAAI,gBAAgB;SAAC,GAAG,uDAAuD;QAEnF,MAAM,SAAS;kDAAC;gBACd,qBAAqB;YACvB;iDAAG;YAAC;SAAa,GAAG,kCAAkC;QACtD,mEAAmE;QACnE,+BAA+B;QAE/B,IAAI,aAAa,YAAY;QAC7B,MAAM,SAAS;kDAAC;gBACd,IAAI,eAAe,QAAQ,eAAe,eAAe;oBACvD,QAAQ,IAAI,CAAC;gBACf;YACF;iDAAG;YAAC;YAAY;SAAc,GAAG,8BAA8B;QAE/D,IAAI,cAAc,YAAY;QAC9B,MAAM,SAAS;kDAAC;gBACd,IAAI,eAAe,MAAM;oBACvB;gBACF;gBAEA,IAAI,WAAW,MAAM;oBACnB,QAAQ,IAAI,CAAC;oBACb;gBACF;gBAEA,IAAI,QAAQ,YAAY,KAAK,aAAa,QAAQ,iBAAiB,KAAK,WAAW;oBACjF,QAAQ,IAAI,CAAC;gBACf;gBAEA,IAAI,YAAY,YAAY,IAAI,QAAQ,QAAQ,YAAY,KAAK,YAAY,YAAY,EAAE;oBACzF,QAAQ,IAAI,CAAC;gBACf;gBAEA,IAAI,YAAY,iBAAiB,IAAI,QAAQ,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB,EAAE;oBACxG,QAAQ,IAAI,CAAC;gBACf;gBAEA,IAAI,YAAY,UAAU,IAAI,QAAQ,QAAQ,UAAU,KAAK,YAAY,UAAU,EAAE;oBACnF,QAAQ,IAAI,CAAC;gBACf;gBAEA,IAAI,YAAY,uBAAuB,IAAI,QAAQ,QAAQ,uBAAuB,KAAK,YAAY,uBAAuB,EAAE;oBAC1H,QAAQ,IAAI,CAAC;gBACf;gBAEA,IAAI,YAAY,iBAAiB,IAAI,QAAQ,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB,EAAE;oBACxG,QAAQ,IAAI,CAAC;gBACf;YACF;iDAAG;YAAC;YAAa;SAAQ;QACzB,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,wBAAwB,QAAQ,EAAE;YACxE,OAAO;QACT,GAAG;IACL;IAEA,IAAI,gCAAgC,SAAS,8BAA8B,IAAI;QAC7E,IAAI,KAAK,KAAK,EAAE,EACZ,YAAY,KAAK,SAAS;QAE9B,IAAI,wBAAwB,8BACxB,mBAAmB,sBAAsB,gBAAgB;QAE7D,IAAI,YAAY,MAAM,MAAM,CAAC;QAC7B,IAAI,UAAU,MAAM,MAAM,CAAC;QAC3B,MAAM,eAAe;6DAAC;gBACpB,IAAI,CAAC,UAAU,OAAO,IAAI,oBAAoB,QAAQ,OAAO,KAAK,MAAM;oBACtE,iBAAiB,KAAK,CAAC,QAAQ,OAAO;oBACtC,UAAU,OAAO,GAAG;gBACtB,EAAE,sBAAsB;gBAGxB;qEAAO;wBACL,IAAI,UAAU,OAAO,IAAI,kBAAkB;4BACzC,IAAI;gCACF,iBAAiB,OAAO;gCACxB,UAAU,OAAO,GAAG;4BACtB,EAAE,OAAO,GAAG;4BACV,wDAAwD;4BACxD,uDAAuD;4BACvD,yDAAyD;4BACzD,yDAAyD;4BACzD,8CAA8C;4BAChD;wBACF;oBACF;;YACF;4DAAG;YAAC;SAAiB;QACrB,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;YAC7C,KAAK;YACL,IAAI;YACJ,WAAW;QACb;IACF,GAAG,mDAAmD;IAGtD,IAAI,gCAAgC,SAAS,8BAA8B,KAAK;QAC9E,IAAI,KAAK,MAAM,EAAE,EACb,YAAY,MAAM,SAAS;QAC/B,mFAAmF;QACnF;QACA,OAAO,WAAW,GAAE,MAAM,aAAa,CAAC,OAAO;YAC7C,IAAI;YACJ,WAAW;QACb;IACF;IAEA,IAAI,mBAAmB,WAAW,gCAAgC;IAElE;;GAEC,GAED,IAAI,YAAY,SAAS;QACvB,IAAI,wBAAwB,2CAA2C,sBACnE,SAAS,sBAAsB,MAAM;QAEzC,OAAO;IACT;IAEA;;;;;GAKC,GAED,IAAI,uBAAuB,uBAAuB,iBAAiB;IACnE;;GAEC,GAED,IAAI,cAAc,uBAAuB,QAAQ;IACjD;;GAEC,GAED,IAAI,oBAAoB,uBAAuB,cAAc;IAC7D;;GAEC,GAED,IAAI,oBAAoB,uBAAuB,cAAc;IAC7D;;GAEC,GAED,IAAI,iBAAiB,uBAAuB,WAAW;IACvD;;GAEC,GAED,IAAI,iBAAiB,uBAAuB,WAAW;IACvD;;GAEC,GAED,IAAI,cAAc,uBAAuB,QAAQ;IACjD;;GAEC,GAED,IAAI,mBAAmB,uBAAuB,aAAa;IAC3D;;GAEC,GAED,IAAI,iBAAiB,uBAAuB,WAAW;IACvD;;GAEC,GAED,IAAI,iBAAiB,uBAAuB,WAAW;IACvD,IAAI,iBAAiB,uBAAuB,WAAW;IACvD;;GAEC,GAED,IAAI,yBAAyB,uBAAuB,mBAAmB;IACvE;;;GAGC,GAED,IAAI,0BAA0B,uBAAuB,oBAAoB;IACzE;;GAEC,GAED,IAAI,8BAA8B,uBAAuB,wBAAwB;IACjF;;GAEC,GAED,IAAI,4BAA4B,uBAAuB,sBAAsB;IAC7E;;GAEC,GAED,IAAI,iBAAiB,uBAAuB,WAAW;IACvD;;;;;GAKC,GAED,IAAI,yBAAyB,uBAAuB,mBAAmB;IACvE;;GAEC,GAED,IAAI,gCAAgC,uBAAuB,0BAA0B;IACrF;;GAEC,GAED,IAAI,uBAAuB,uBAAuB,iBAAiB;IACnE;;GAEC,GAED,IAAI,iCAAiC,uBAAuB,2BAA2B;IAEvF,SAAQ,cAAc,GAAG;IACzB,SAAQ,oBAAoB,GAAG;IAC/B,SAAQ,8BAA8B,GAAG;IACzC,SAAQ,oBAAoB,GAAG;IAC/B,SAAQ,cAAc,GAAG;IACzB,SAAQ,WAAW,GAAG;IACtB,SAAQ,iBAAiB,GAAG;IAC5B,SAAQ,iBAAiB,GAAG;IAC5B,SAAQ,gBAAgB,GAAG;IAC3B,SAAQ,uBAAuB,GAAG;IAClC,SAAQ,QAAQ,GAAG;IACnB,SAAQ,gBAAgB,GAAG;IAC3B,SAAQ,gBAAgB,GAAG;IAC3B,SAAQ,wBAAwB,GAAG;IACnC,SAAQ,cAAc,GAAG;IACzB,SAAQ,sBAAsB,GAAG;IACjC,SAAQ,cAAc,GAAG;IACzB,SAAQ,WAAW,GAAG;IACtB,SAAQ,gBAAgB,GAAG;IAC3B,SAAQ,yBAAyB,GAAG;IACpC,SAAQ,cAAc,GAAG;IACzB,SAAQ,cAAc,GAAG;IACzB,SAAQ,6BAA6B,GAAG;IACxC,SAAQ,2BAA2B,GAAG;IACtC,SAAQ,sBAAsB,GAAG;IACjC,SAAQ,WAAW,GAAG;IACtB,SAAQ,WAAW,GAAG;IACtB,SAAQ,SAAS,GAAG;AAEtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2520, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2556, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2583, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2623, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2655, "column": 0}, "map": {"version": 3, "file": "credit-card.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/credit-card.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '14', x: '2', y: '5', rx: '2', key: 'ynyp8z' }],\n  ['line', { x1: '2', x2: '22', y1: '10', y2: '10', key: '1b3vmo' }],\n];\n\n/**\n * @component @name CreditCard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjUiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIxMCIgeTI9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/credit-card\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CreditCard = createLucideIcon('credit-card', __iconNode);\n\nexport default CreditCard;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2747, "column": 0}, "map": {"version": 3, "file": "shield.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/shield.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z',\n      key: 'oel41y',\n    },\n  ],\n];\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTNjMCA1LTMuNSA3LjUtNy42NiA4Ljk1YTEgMSAwIDAgMS0uNjctLjAxQzcuNSAyMC41IDQgMTggNCAxM1Y2YTEgMSAwIDAgMSAxLTFjMiAwIDQuNS0xLjIgNi4yNC0yLjcyYTEuMTcgMS4xNyAwIDAgMSAxLjUyIDBDMTQuNTEgMy44MSAxNyA1IDE5IDVhMSAxIDAgMCAxIDEgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('shield', __iconNode);\n\nexport default Shield;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2786, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}