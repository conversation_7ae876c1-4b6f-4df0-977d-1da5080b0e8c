{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\n\nexport const routing = defineRouting({\n  // 支持的语言列表\n  locales: ['en', 'zh'],\n\n  // 默认语言\n  defaultLocale: 'en',\n\n  // 语言前缀配置 - 始终显示语言前缀\n  localePrefix: 'always',\n\n  // 启用语言检测\n  localeDetection: true,\n\n  // 启用备用链接\n  alternateLinks: true,\n\n  // 语言 cookie 配置\n  localeCookie: {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 * 365 // 1 year\n  }\n});\n\n// 轻量级的包装器，围绕 Next.js 的导航 API\n// 它们将自动处理用户的语言环境\nexport const { Link, redirect, usePathname, useRouter } = createNavigation(routing);\n\n// 导出安全路由助手（推荐在客户端组件中使用）\nexport { useSafeRouter, useSafePathname, safeLocationHref, createSafeHref, useUrlValidation, withUrlValidation } from '@/lib/safe-routing';\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AA8BA,wBAAwB;AACxB;;;AA7BO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,UAAU;IACV,SAAS;QAAC;QAAM;KAAK;IAErB,OAAO;IACP,eAAe;IAEf,oBAAoB;IACpB,cAAc;IAEd,SAAS;IACT,iBAAiB;IAEjB,SAAS;IACT,gBAAgB;IAEhB,eAAe;IACf,cAAc;QACZ,MAAM;QACN,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK,IAAI,SAAS;IACtC;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/url-utils.ts"], "sourcesContent": ["// URL工具函数 - 处理国际化URL的规范化\n\n// 支持的语言列表\nconst locales = ['en', 'zh'] as const;\nconst defaultLocale = 'en';\n\nexport type Locale = typeof locales[number];\n\n/**\n * 规范化URL路径，确保正确的locale前缀\n * @param path - 要规范化的路径\n * @param currentLocale - 当前语言环境\n * @returns 规范化后的路径\n */\nexport function normalizeUrlPath(path: string, currentLocale: string = defaultLocale): string {\n  // 移除开头和结尾的斜杠\n  const cleanPath = path.replace(/^\\/+|\\/+$/g, '');\n  \n  // 如果路径为空，返回根路径\n  if (!cleanPath) {\n    return '/';\n  }\n  \n  // 分割路径段\n  const segments = cleanPath.split('/').filter(Boolean);\n  \n  if (segments.length === 0) {\n    return '/';\n  }\n  \n  const firstSegment = segments[0];\n  \n  // 检查第一个段是否是locale\n  if (locales.includes(firstSegment as Locale)) {\n    // 检查是否有重复的locale\n    const secondSegment = segments[1];\n    if (secondSegment && locales.includes(secondSegment as Locale)) {\n      // 移除重复的locale，保留第二个\n      return '/' + segments.slice(1).join('/');\n    }\n    \n    // 如果已经有正确的locale前缀，直接返回\n    return '/' + segments.join('/');\n  }\n  \n  // 如果没有locale前缀，添加当前locale\n  return `/${currentLocale}/${segments.join('/')}`;\n}\n\n/**\n * 检查URL是否包含有效的locale前缀\n * @param path - 要检查的路径\n * @returns 是否包含有效的locale前缀\n */\nexport function hasValidLocalePrefix(path: string): boolean {\n  const segments = path.replace(/^\\/+/, '').split('/').filter(Boolean);\n  return segments.length > 0 && locales.includes(segments[0] as Locale);\n}\n\n/**\n * 从URL中提取locale\n * @param path - URL路径\n * @returns 提取的locale或默认locale\n */\nexport function extractLocaleFromPath(path: string): Locale {\n  const segments = path.replace(/^\\/+/, '').split('/').filter(Boolean);\n  const firstSegment = segments[0];\n  \n  if (firstSegment && locales.includes(firstSegment as Locale)) {\n    return firstSegment as Locale;\n  }\n  \n  return defaultLocale;\n}\n\n/**\n * 移除URL中的locale前缀\n * @param path - URL路径\n * @returns 移除locale前缀后的路径\n */\nexport function removeLocalePrefix(path: string): string {\n  const segments = path.replace(/^\\/+/, '').split('/').filter(Boolean);\n  \n  if (segments.length > 0 && locales.includes(segments[0] as Locale)) {\n    const remainingPath = segments.slice(1).join('/');\n    return remainingPath ? `/${remainingPath}` : '/';\n  }\n  \n  return path;\n}\n\n/**\n * 为URL添加locale前缀\n * @param path - URL路径\n * @param locale - 要添加的locale\n * @returns 添加locale前缀后的路径\n */\nexport function addLocalePrefix(path: string, locale: Locale = defaultLocale): string {\n  const cleanPath = removeLocalePrefix(path);\n  \n  if (cleanPath === '/') {\n    return `/${locale}`;\n  }\n  \n  return `/${locale}${cleanPath}`;\n}\n\n/**\n * 安全的URL构建函数，确保正确的locale处理\n * @param path - 基础路径\n * @param locale - 语言环境\n * @param params - URL参数\n * @returns 完整的URL\n */\nexport function buildSafeUrl(\n  path: string, \n  locale: string = defaultLocale, \n  params?: Record<string, string | number>\n): string {\n  // 规范化路径\n  const normalizedPath = normalizeUrlPath(path, locale);\n  \n  // 添加查询参数\n  if (params && Object.keys(params).length > 0) {\n    const searchParams = new URLSearchParams();\n    Object.entries(params).forEach(([key, value]) => {\n      searchParams.append(key, String(value));\n    });\n    return `${normalizedPath}?${searchParams.toString()}`;\n  }\n  \n  return normalizedPath;\n}\n\n/**\n * 验证并修复URL\n * @param url - 要验证的URL\n * @param currentLocale - 当前语言环境\n * @returns 修复后的URL和是否需要重定向的标志\n */\nexport function validateAndFixUrl(url: string, currentLocale: string = defaultLocale): {\n  fixedUrl: string;\n  needsRedirect: boolean;\n} {\n  try {\n    const urlObj = new URL(url, 'http://localhost');\n    const originalPath = urlObj.pathname;\n    const normalizedPath = normalizeUrlPath(originalPath, currentLocale);\n    \n    const needsRedirect = originalPath !== normalizedPath;\n    const fixedUrl = needsRedirect ? \n      `${normalizedPath}${urlObj.search}${urlObj.hash}` : \n      url;\n    \n    return { fixedUrl, needsRedirect };\n  } catch {\n    // 如果URL解析失败，返回安全的默认值\n    return { \n      fixedUrl: `/${currentLocale}`, \n      needsRedirect: true \n    };\n  }\n}\n\n// 导出常量\nexport { locales, defaultLocale };\n"], "names": [], "mappings": "AAAA,yBAAyB;AAEzB,UAAU;;;;;;;;;;;;AACV,MAAM,UAAU;IAAC;IAAM;CAAK;AAC5B,MAAM,gBAAgB;AAUf,SAAS,iBAAiB,IAAY,EAAE,gBAAwB,aAAa;IAClF,aAAa;IACb,MAAM,YAAY,KAAK,OAAO,CAAC,cAAc;IAE7C,eAAe;IACf,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,QAAQ;IACR,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,MAAM,CAAC;IAE7C,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,MAAM,eAAe,QAAQ,CAAC,EAAE;IAEhC,kBAAkB;IAClB,IAAI,QAAQ,QAAQ,CAAC,eAAyB;QAC5C,iBAAiB;QACjB,MAAM,gBAAgB,QAAQ,CAAC,EAAE;QACjC,IAAI,iBAAiB,QAAQ,QAAQ,CAAC,gBAA0B;YAC9D,oBAAoB;YACpB,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,IAAI,CAAC;QACtC;QAEA,wBAAwB;QACxB,OAAO,MAAM,SAAS,IAAI,CAAC;IAC7B;IAEA,0BAA0B;IAC1B,OAAO,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,SAAS,IAAI,CAAC,MAAM;AAClD;AAOO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,WAAW,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5D,OAAO,SAAS,MAAM,GAAG,KAAK,QAAQ,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC5D;AAOO,SAAS,sBAAsB,IAAY;IAChD,MAAM,WAAW,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5D,MAAM,eAAe,QAAQ,CAAC,EAAE;IAEhC,IAAI,gBAAgB,QAAQ,QAAQ,CAAC,eAAyB;QAC5D,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,WAAW,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC;IAE5D,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAa;QAClE,MAAM,gBAAgB,SAAS,KAAK,CAAC,GAAG,IAAI,CAAC;QAC7C,OAAO,gBAAgB,CAAC,CAAC,EAAE,eAAe,GAAG;IAC/C;IAEA,OAAO;AACT;AAQO,SAAS,gBAAgB,IAAY,EAAE,SAAiB,aAAa;IAC1E,MAAM,YAAY,mBAAmB;IAErC,IAAI,cAAc,KAAK;QACrB,OAAO,CAAC,CAAC,EAAE,QAAQ;IACrB;IAEA,OAAO,CAAC,CAAC,EAAE,SAAS,WAAW;AACjC;AASO,SAAS,aACd,IAAY,EACZ,SAAiB,aAAa,EAC9B,MAAwC;IAExC,QAAQ;IACR,MAAM,iBAAiB,iBAAiB,MAAM;IAE9C,SAAS;IACT,IAAI,UAAU,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;QAC5C,MAAM,eAAe,IAAI;QACzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,aAAa,MAAM,CAAC,KAAK,OAAO;QAClC;QACA,OAAO,GAAG,eAAe,CAAC,EAAE,aAAa,QAAQ,IAAI;IACvD;IAEA,OAAO;AACT;AAQO,SAAS,kBAAkB,GAAW,EAAE,gBAAwB,aAAa;IAIlF,IAAI;QACF,MAAM,SAAS,IAAI,IAAI,KAAK;QAC5B,MAAM,eAAe,OAAO,QAAQ;QACpC,MAAM,iBAAiB,iBAAiB,cAAc;QAEtD,MAAM,gBAAgB,iBAAiB;QACvC,MAAM,WAAW,gBACf,GAAG,iBAAiB,OAAO,MAAM,GAAG,OAAO,IAAI,EAAE,GACjD;QAEF,OAAO;YAAE;YAAU;QAAc;IACnC,EAAE,OAAM;QACN,qBAAqB;QACrB,OAAO;YACL,UAAU,CAAC,CAAC,EAAE,eAAe;YAC7B,eAAe;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/safe-routing.ts"], "sourcesContent": ["'use client';\n\n// 安全路由助手 - 包装next-intl路由功能，提供额外的URL安全检查\n\nimport React from 'react';\nimport { useRouter as useNextIntlRouter, usePathname as useNextIntlPathname } from '@/i18n/routing';\nimport { useLocale } from 'next-intl';\nimport { normalizeUrlPath, buildSafeUrl, validateAndFixUrl } from './url-utils';\n\n/**\n * 增强的useRouter hook，提供安全的路由导航\n */\nexport function useSafeRouter() {\n  const router = useNextIntlRouter();\n  const locale = useLocale();\n\n  return {\n    ...router,\n    \n    /**\n     * 安全的push方法，自动处理locale前缀\n     */\n    push: (href: string, options?: any) => {\n      const normalizedHref = normalizeUrlPath(href, locale);\n      console.log(`🔄 Safe Router Push: ${href} -> ${normalizedHref}`);\n      return router.push(normalizedHref, options);\n    },\n    \n    /**\n     * 安全的replace方法，自动处理locale前缀\n     */\n    replace: (href: string, options?: any) => {\n      const normalizedHref = normalizeUrlPath(href, locale);\n      console.log(`🔄 Safe Router Replace: ${href} -> ${normalizedHref}`);\n      return router.replace(normalizedHref, options);\n    },\n    \n    /**\n     * 安全的prefetch方法，自动处理locale前缀\n     */\n    prefetch: (href: string, options?: any) => {\n      const normalizedHref = normalizeUrlPath(href, locale);\n      return router.prefetch(normalizedHref, options);\n    },\n    \n    /**\n     * 构建安全的URL\n     */\n    buildUrl: (path: string, params?: Record<string, string | number>) => {\n      return buildSafeUrl(path, locale, params);\n    },\n    \n    /**\n     * 验证当前URL并在需要时重定向\n     */\n    validateCurrentUrl: () => {\n      if (typeof window !== 'undefined') {\n        const currentUrl = window.location.pathname + window.location.search;\n        const { fixedUrl, needsRedirect } = validateAndFixUrl(currentUrl, locale);\n        \n        if (needsRedirect) {\n          console.log(`🔄 URL Validation Redirect: ${currentUrl} -> ${fixedUrl}`);\n          router.replace(fixedUrl);\n          return true;\n        }\n      }\n      return false;\n    }\n  };\n}\n\n/**\n * 增强的usePathname hook，提供安全的路径名获取\n */\nexport function useSafePathname() {\n  const pathname = useNextIntlPathname();\n  const locale = useLocale();\n  \n  return {\n    /**\n     * 获取当前路径名\n     */\n    pathname,\n    \n    /**\n     * 获取规范化的路径名\n     */\n    normalizedPathname: normalizeUrlPath(pathname, locale),\n    \n    /**\n     * 检查当前路径是否需要规范化\n     */\n    needsNormalization: pathname !== normalizeUrlPath(pathname, locale),\n    \n    /**\n     * 获取不含locale前缀的路径\n     */\n    pathWithoutLocale: pathname.replace(new RegExp(`^/${locale}`), '') || '/'\n  };\n}\n\n/**\n * 安全的window.location.href设置函数\n */\nexport function safeLocationHref(url: string, currentLocale?: string) {\n  if (typeof window === 'undefined') return;\n  \n  const locale = currentLocale || 'en';\n  const { fixedUrl } = validateAndFixUrl(url, locale);\n  \n  console.log(`🔄 Safe Location Href: ${url} -> ${fixedUrl}`);\n  window.location.href = fixedUrl;\n}\n\n/**\n * 创建安全的链接href\n */\nexport function createSafeHref(path: string, locale?: string, params?: Record<string, string | number>): string {\n  return buildSafeUrl(path, locale, params);\n}\n\n/**\n * React Hook：在组件挂载时验证URL\n */\nexport function useUrlValidation() {\n  const router = useSafeRouter();\n  \n  // 在客户端挂载时验证URL\n  if (typeof window !== 'undefined') {\n    // 使用setTimeout确保在React hydration完成后执行\n    setTimeout(() => {\n      router.validateCurrentUrl();\n    }, 0);\n  }\n}\n\n/**\n * 高阶组件：为组件添加URL验证功能\n */\nexport function withUrlValidation<P extends object>(Component: React.ComponentType<P>) {\n  return function UrlValidatedComponent(props: P) {\n    useUrlValidation();\n    return React.createElement(Component, props);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA,wCAAwC;AAExC;AACA;AAAA;AACA;AACA;;AAPA;;;;;AAYO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,yIAAA,CAAA,YAAiB,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,OAAO;QACL,GAAG,MAAM;QAET;;KAEC,GACD,MAAM,CAAC,MAAc;YACnB,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;YAC9C,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE,gBAAgB;YAC/D,OAAO,OAAO,IAAI,CAAC,gBAAgB;QACrC;QAEA;;KAEC,GACD,SAAS,CAAC,MAAc;YACtB,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;YAC9C,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE,gBAAgB;YAClE,OAAO,OAAO,OAAO,CAAC,gBAAgB;QACxC;QAEA;;KAEC,GACD,UAAU,CAAC,MAAc;YACvB,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;YAC9C,OAAO,OAAO,QAAQ,CAAC,gBAAgB;QACzC;QAEA;;KAEC,GACD,UAAU,CAAC,MAAc;YACvB,OAAO,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ;QACpC;QAEA;;KAEC,GACD,oBAAoB;YAClB,wCAAmC;gBACjC,MAAM,aAAa,OAAO,QAAQ,CAAC,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM;gBACpE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;gBAElE,IAAI,eAAe;oBACjB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,WAAW,IAAI,EAAE,UAAU;oBACtE,OAAO,OAAO,CAAC;oBACf,OAAO;gBACT;YACF;YACA,OAAO;QACT;IACF;AACF;GAzDgB;;QACC,yIAAA,CAAA,YAAiB;QACjB,qKAAA,CAAA,YAAS;;;AA4DnB,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,cAAmB,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,OAAO;QACL;;KAEC,GACD;QAEA;;KAEC,GACD,oBAAoB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;QAE/C;;KAEC,GACD,oBAAoB,aAAa,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;QAE5D;;KAEC,GACD,mBAAmB,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,QAAQ,GAAG,OAAO;IACxE;AACF;IAzBgB;;QACG,yIAAA,CAAA,cAAmB;QACrB,qKAAA,CAAA,YAAS;;;AA4BnB,SAAS,iBAAiB,GAAW,EAAE,aAAsB;IAClE,uCAAmC;;IAAM;IAEzC,MAAM,SAAS,iBAAiB;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;IAE5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,IAAI,EAAE,UAAU;IAC1D,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB;AAKO,SAAS,eAAe,IAAY,EAAE,MAAe,EAAE,MAAwC;IACpG,OAAO,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ;AACpC;AAKO,SAAS;;IACd,MAAM,SAAS;IAEf,eAAe;IACf,wCAAmC;QACjC,sCAAsC;QACtC,WAAW;YACT,OAAO,kBAAkB;QAC3B,GAAG;IACL;AACF;IAVgB;;QACC;;;AAcV,SAAS,kBAAoC,SAAiC;;IACnF,UAAO,SAAS,sBAAsB,KAAQ;;QAC5C;QACA,qBAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;IACxC;;YAFE;;;AAGJ", "debugId": null}}]}